

/**
 * Creates a preview URL by adding a port number to a host URL
 * @param {string} hostUrl - The base host URL
 * @param {number|string} portNumber - The port number to append
 * @returns {string} - The complete preview URL with port
 */
const getPreviewUrl = (hostUrl, portNumber) => {
  try {
    // Parse the host URL
    const url = new URL(hostUrl);
    
    // Remove any existing port
    url.port = '';
    
    // Get the base URL without any path or query parameters
    const baseUrl = `${url.protocol}//${url.hostname}`;
    
    // Return the base URL with the port appended
    return `${baseUrl}:${portNumber}`;
  } catch (error) {
    
    return null;
  }
};

export { getPreviewUrl };



/**
 * Changes the host of a URL while preserving or handling port numbers
 * @param {string} url - The original URL
 * @param {string} newHost - The new host to use
 * @returns {string} - The URL with the updated host
 */
const changeUrlHost = (url, newHost) => {
  try {
    // Parse the original URL
    const parsedUrl = new URL(url);
    
    // Store the original port
    const originalPort = parsedUrl.port;
    
    // Parse the new host to ensure it's valid
    // If newHost includes protocol, use it; otherwise use the original protocol
    let newUrl;
    try {
      // Check if newHost has a protocol
      if (!/^https?:\/\//i.test(newHost)) {
        // No protocol, so prepend the original protocol
        newHost = `${parsedUrl.protocol}//${newHost}`;
      }
      newUrl = new URL(newHost);
    } catch (error) {
      // If newHost is not a valid URL, assume it's just a hostname
      newUrl = new URL(`${parsedUrl.protocol}//${newHost}`);
    }
    
    // Preserve the path, query, and hash from the original URL
    newUrl.pathname = parsedUrl.pathname;
    newUrl.search = parsedUrl.search;
    newUrl.hash = parsedUrl.hash;
    
    // Handle port based on requirements
    if (originalPort) {
      // If original URL had a port, use it
      newUrl.port = originalPort;
    }
    // If no port in the original URL, leave it as is (no port)
    
    return newUrl.toString();
  } catch (error) {
    
    return null;
  }
};

export { changeUrlHost };
// Example usage:
// const hostUrl = "https://example.com";
// const portNumber = 8080;
// const previewUrl = getPreviewUrl(hostUrl, portNumber);
//  // Output: "https://example.com:8080"

/**
 * Extracts the host from a URL
 * @param {string} url - The URL to extract the host from
 * @param {boolean} includeProtocol - Whether to include the protocol in the result
 * @returns {string} - The host of the URL
 */
const getUrlHost = (url, includeProtocol = false) => {
  try {
    // Parse the URL
    const parsedUrl = new URL(url);
    
    // Return the host with or without protocol based on the parameter
    return includeProtocol 
      ? `${parsedUrl.protocol}//${parsedUrl.hostname}`
      : parsedUrl.hostname;
  } catch (error) {
    
    return null;
  }
};

/**
 * Extracts the port from a URL
 * @param {string} url - The URL to extract the port from
 * @param {string|number} defaultPort - The default port to return if none exists
 * @returns {string|number|null} - The port of the URL or the default port if none exists
 */
const getUrlPort = (url, defaultPort = null) => {
  try {
    // Parse the URL
    const parsedUrl = new URL(url);
    
    // Return the port if it exists, otherwise return the default port
    return parsedUrl.port ? parsedUrl.port : defaultPort;
  } catch (error) {
    
    return null;
  }
};

/**
 * Extracts pod_id and stage_name from a given URL and constructs the internal service URL
 * @param {string} url - The iframe URL from code generation context
 * @returns {object} - Object containing pod_id, stage_name, and constructed internal URL
 */
function getPodIdAndStageFromUrl(url) {
  // Handle localhost case
  if (!url || url.includes('localhost')) {
    return {
      pod_id: 'localhost',
      stage_name: 'local',
      internal_url: 'http://localhost:8003/api/code_gen',
      is_localhost: true
    };
  }

  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;
    
    // Pattern: vscode-internal-{pod_id}-{stage}.{stage}01.cloud.kavia.ai
    // Example: vscode-internal-25460-qa.qa01.cloud.kavia.ai
    const match = hostname.match(/^vscode-internal-(\d+)-([^.]+)\.([^.]+)01\.cloud\.kavia\.ai$/);
    
    if (match) {
      const pod_id = match[1];      // 25460
      const stage_name = match[2];  // qa
      const stage_verify = match[3]; // qa (should match stage_name)
      
      // Verify that both stage references match
      if (stage_name !== stage_verify) {
        console.warn(`Stage name mismatch: ${stage_name} vs ${stage_verify}`);
      }
      
      // Construct the internal service URL
      const internal_url = `http://internal-${pod_id}-${stage_name}.duploservices-k-${stage_name}01.svc.cluster.local:8003/api/code_gen`;
      
      return {
        pod_id,
        stage_name,
        internal_url,
        is_localhost: false,
        original_url: url
      };
    }
    
    // If pattern doesn't match, return null values
    return {
      pod_id: null,
      stage_name: null,
      internal_url: null,
      is_localhost: false,
      error: 'URL pattern not recognized',
      original_url: url
    };
    
  } catch (error) {
    return {
      pod_id: null,
      stage_name: null,
      internal_url: null,
      is_localhost: false,
      error: `Invalid URL: ${error.message}`,
      original_url: url
    };
  }
}




export { getUrlHost, getUrlPort, getPodIdAndStageFromUrl };

import { getHeaders } from "./api";

const base_url = process.env.NEXT_PUBLIC_API_URL;
const SHOW_NAME = 'scm';

/**
 * Configure SCM settings
 * @param {Object} config - SCM configuration object
 * @returns {Promise<Object>} Response from the server
 */
export const configureSCM = async (config) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/configure`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify(config)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to configure SCM');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

/**
 * Get SCM configuration(s)
 * @param {string} [scmId] - Optional SCM ID to get specific configuration
 */
export const getSCMConfiguration = async (scmId = null, isEncrypted = true) => {
  try {
    
    const url = new URL(`${base_url}/${SHOW_NAME}/configuration`);
    if (scmId) {
      url.searchParams.append('scm_id', scmId);
      url.searchParams.append('is_encrypted', isEncrypted);
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to get SCM configuration');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

/**
 * Delete SCM configuration
 * @param {string} [scmId] - Optional SCM ID to delete specific configuration
 * @returns {Promise<Object>} Response from the server
 */
export const deleteSCMConfiguration = async (scmId = null, isEncrypted = true) => {
  try {
    const url = new URL(`${base_url}/${SHOW_NAME}/configuration`);
    if (scmId) {
      url.searchParams.append('scm_id', scmId);
      if(isEncrypted){
        url.searchParams.append('is_encrypted', isEncrypted);
      }
    }

    const response = await fetch(url, {
      method: 'DELETE',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to delete SCM configuration');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

/**
 * Get OAuth login URL
 * @param {string} scmType - Type of SCM (github/gitlab)
 */
export const getOAuthLoginURL = async (scmType) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/oauth/${scmType}/login?return_url=true`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to get OAuth login URL');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

/**
 * Handle OAuth callback
 * @param {string} scmType - Type of SCM (github/gitlab)
 * @param {string} state - OAuth state parameter
 * @param {string} code - OAuth code parameter
 */
export const handleOAuthCallback = async (scmType, state, code) => {
  try {
    const url = new URL(`${base_url}/${SHOW_NAME}/oauth/${scmType}/callback`);
    url.searchParams.append('state', state);
    url.searchParams.append('code', code);

    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to handle OAuth callback');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

// SCM Types enum
export const SCMType = {
  GITHUB: 'github',
  GITLAB: 'gitlab'
};

// SCM Auth Types enum
export const SCMAuthType = {
  OAUTH: 'oauth',
  PAT: 'pat',
  SSH: 'ssh'
};


/**
 * Get all GitHub SCM configurations for the current tenant
 * @returns {Promise<Object>} Response containing GitHub configurations
 */
export const getGitHubConfigurations = async () => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/github/configurations`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to get GitHub configurations');
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};


export const getGitLabConfigurations = async () => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/gitlab/configurations`, {
      method: 'GET',
      headers: await getHeaders()
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching GitLab configurations:', error);
    throw error;
  }
};

/**
 * Save Gerrit SCM configuration
 * @param {Object} config - Gerrit configuration object
 * @param {string} config.username - Gerrit username
 * @param {string} config.password - Gerrit password (HTTP password)
 * @param {string} config.hostname - Gerrit hostname/server URL
 * @returns {Promise<Object>} Response from the server
 */
export const saveGerritConfig = async (config) => {
  try {
    const { username, password, hostname } = config;
    
    // Validate required fields
    if (!username || !password || !hostname) {
      throw new Error('Username, password, and hostname are required');
    }

    const response = await fetch(`${base_url}/${SHOW_NAME}/gerrit/configure`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({
        username,
        password,
        hostname
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to save Gerrit configuration');
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

/**
 * Set SCM configuration as default
 * @param {string} scmType - The SCM type (github, gitlab, gerrit, etc.)
 * @param {boolean} isPrimary - Whether to set as primary (true) or unset (false)
 * @returns {Promise<Object>} Response from the server
 */
export const setAsDefault = async (scmType, isPrimary = true) => {
  try {
    const url = new URL(`${base_url}/${SHOW_NAME}/set_as_default`);
    url.searchParams.append('scm_type', scmType);
    url.searchParams.append('is_primary', isPrimary.toString());

    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to set SCM as default');
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};
/**
 * Service Configuration Helper for Frontend
 * 
 * This utility helps determine which services require OAuth flow vs manual setup,
 * making the system scalable for future services.
 */

// Service authentication types
export const ServiceAuthType = {
  OAUTH: 'oauth',
  MANUAL: 'manual'
};

// Services that require OAuth flow
const OAUTH_SERVICES = {
  supabase: {
    authType: ServiceAuthType.OAUTH,
    displayName: 'Supabase',
    description: 'Database and backend services',
    helpUrl: 'https://supabase.com/docs/guides/api',
    envVars: ['SUPABASE_URL', 'SUPABASE_KEY']
  },
};

// Services that require manual API key setup
const MANUAL_SERVICES = {
  openai: {
    authType: ServiceAuthType.MANUAL,
    displayName: 'OpenAI',
    description: 'AI language models',
    helpUrl: 'https://platform.openai.com/docs/quickstart',
    envVars: ['OPENAI_API_KEY'],
    instructions: `To use OpenAI's GPT models:
1. Sign up at https://platform.openai.com
2. Navigate to API Keys section
3. Create a new API key
4. Enter it below`
  },
  openweathermap: {
    authType: ServiceAuthType.MANUAL,
    displayName: 'OpenWeatherMap',
    description: 'Weather data services',
    helpUrl: 'https://openweathermap.org/api',
    envVars: ['OPENWEATHERMAP_API_KEY'],
    instructions: `To use OpenWeatherMap API:
1. Sign up at https://openweathermap.org
2. Go to API keys section
3. Generate a new API key
4. Enter it below`
  },
  firebase: {
    authType: ServiceAuthType.MANUAL,
    displayName: 'Firebase',
    description: "Google's app development platform",
    helpUrl: 'https://firebase.google.com/docs',
    envVars: ['FIREBASE_CONFIG'],
    instructions: `To use Firebase:
1. Create a project in Firebase Console
2. Go to Project Settings
3. Copy the configuration object
4. Enter it below`
  }
};

/**
 * Get the authentication type for a service
 * @param {string} serviceName - Name of the service (e.g., 'supabase', 'openai')
 * @returns {string|null} ServiceAuthType or null if service not found
 */
export function getServiceAuthType(serviceName) {
  const service = serviceName?.toLowerCase();
  
  if (OAUTH_SERVICES[service]) {
    return ServiceAuthType.OAUTH;
  } else if (MANUAL_SERVICES[service]) {
    return ServiceAuthType.MANUAL;
  }
  
  return null;
}

/**
 * Check if a service requires OAuth flow
 * @param {string} serviceName - Name of the service
 * @returns {boolean} True if service requires OAuth, false otherwise
 */
export function requiresOAuth(serviceName) {
  return getServiceAuthType(serviceName) === ServiceAuthType.OAUTH;
}

/**
 * Check if a service requires manual API key setup
 * @param {string} serviceName - Name of the service
 * @returns {boolean} True if service requires manual setup, false otherwise
 */
export function requiresManualSetup(serviceName) {
  return getServiceAuthType(serviceName) === ServiceAuthType.MANUAL;
}

/**
 * Get complete configuration for a service
 * @param {string} serviceName - Name of the service
 * @returns {Object|null} Service configuration object or null if not found
 */
export function getServiceConfig(serviceName) {
  const service = serviceName?.toLowerCase();
  
  if (OAUTH_SERVICES[service]) {
    return OAUTH_SERVICES[service];
  } else if (MANUAL_SERVICES[service]) {
    return MANUAL_SERVICES[service];
  }
  
  return null;
}

/**
 * Get list of all OAuth services
 * @returns {string[]} Array of OAuth service names
 */
export function getOAuthServices() {
  return Object.keys(OAUTH_SERVICES);
}

/**
 * Get list of all manual setup services
 * @returns {string[]} Array of manual setup service names
 */
export function getManualServices() {
  return Object.keys(MANUAL_SERVICES);
}

/**
 * Get list of all supported services
 * @returns {string[]} Array of all service names
 */
export function getAllServices() {
  return [...Object.keys(OAUTH_SERVICES), ...Object.keys(MANUAL_SERVICES)];
}

/**
 * Determine if an external action request should use OAuth flow
 * This is the main function used by the frontend to decide which UI to show
 * @param {Object} externalActionRequest - The action request object from the agent
 * @returns {boolean} True if should use OAuth flow, false for manual setup
 */
export function shouldUseOAuthFlow(externalActionRequest) {
  // First check if oauth_flow is explicitly set (backward compatibility)
  if (externalActionRequest?.oauth_flow === true) {
    return true;
  }
  
  // Then check if action_type is setup_service_oauth
  if (externalActionRequest?.action_type === 'setup_service_oauth') {
    return true;
  }
  
  // Finally check service name against our configuration
  if (externalActionRequest?.service_name) {
    return requiresOAuth(externalActionRequest.service_name);
  }
  
  return false;
}

/**
 * Get the appropriate component name for a service
 * @param {string} serviceName - Name of the service
 * @returns {string} Component name to use for OAuth setup
 */
export function getOAuthComponentName(serviceName) {
  const service = serviceName?.toLowerCase();
  
  switch (service) {
    case 'supabase':
      return 'SupabaseOAuthChatSetup';
    // case 'stripe':
    //   return 'StripeOAuthChatSetup';
    default:
      return null;
  }
}

/**
 * Validate if a service is supported
 * @param {string} serviceName - Name of the service
 * @returns {boolean} True if service is supported, false otherwise
 */
export function isServiceSupported(serviceName) {
  return getServiceConfig(serviceName) !== null;
}

/**
 * Get display name for a service
 * @param {string} serviceName - Name of the service
 * @returns {string} Display name or the service name if not found
 */
export function getServiceDisplayName(serviceName) {
  const config = getServiceConfig(serviceName);
  return config?.displayName || serviceName;
}

// Default export with all functions
export default {
  ServiceAuthType,
  getServiceAuthType,
  requiresOAuth,
  requiresManualSetup,
  getServiceConfig,
  getOAuthServices,
  getManualServices,
  getAllServices,
  shouldUseOAuthFlow,
  getOAuthComponentName,
  isServiceSupported,
  getServiceDisplayName
};

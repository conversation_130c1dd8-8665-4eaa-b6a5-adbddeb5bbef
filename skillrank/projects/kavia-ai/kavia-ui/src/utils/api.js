// utils/api.js
"use client";

import Cookies from "js-cookie";
import { decrypt, setCookie, removeCookie, isTokenExpired, isTokenValid } from "./auth";
import { encryptTenantId } from "./hash";
import { fetchEventSource } from "@microsoft/fetch-event-source";

export const backend_base_url = process.env.NEXT_PUBLIC_API_URL;

// Utility function for properly handling SSE connections
async function createSSEConnection(url, options = {}) {
  const {
    method = "POST",
    headers = {},
    body = null,
    timeout = 300000, // 5 minutes default
    onMessage = () => {},
    onError = () => {},
    onOpen = () => {},
    onClose = () => {}
  } = options;

  return new Promise((resolve, reject) => {
    const abortController = new AbortController();
    let isCompleted = false;
    let finalResult = null;
    let messages = [];

    // Set a timeout to prevent infinite connections
    const timeoutId = setTimeout(() => {
      if (!isCompleted) {
        console.warn('SSE connection timeout - aborting');
        abortController.abort();
        finalResult = {
          error: "Connection timeout",
          message: "The request timed out. Please try again.",
          timeout: true,
          task_id: null
        };
        isCompleted = true;
        resolve(finalResult);
      }
    }, timeout);

    const cleanup = () => {
      clearTimeout(timeoutId);
      if (!isCompleted) {
        isCompleted = true;
      }
    };

    fetchEventSource(url, {
      method,
      headers: {
        ...headers,
        'Content-Type': 'application/json',
      },
      body,
      signal: abortController.signal,
      openWhenHidden: true,
      onopen: async (response) => {
        if (response.status === 402) {
          cleanup();
          abortController.abort();
          reject(new Error('Payment required'));
          return;
        }
        if (!response.ok) {
          cleanup();
          abortController.abort();
          reject(new Error(`HTTP ${response.status}: ${response.statusText}`));
          return;
        }
        
        onOpen(response);
      },
      onmessage(event) {
        try {
          if (isCompleted) {
            return;
          }

          const parsedData = JSON.parse(event.data);
          
          messages.push(parsedData);

          // Call the custom message handler
          const result = onMessage(parsedData, messages, {
            abort: () => {
              if (!isCompleted) {
                cleanup();
                abortController.abort();
                isCompleted = true;
              }
            },
            complete: (result) => {
              if (!isCompleted) {
                finalResult = result;
                cleanup();
                abortController.abort();
                isCompleted = true;
                resolve(finalResult);
              }
            }
          });

          // Handle return value from onMessage
          if (result && result.complete) {
            if (!isCompleted) {
              finalResult = result.data;
              cleanup();
              abortController.abort();
              isCompleted = true;
              resolve(finalResult);
            }
          }

        } catch (error) {
          console.warn('Failed to parse SSE data:', event.data, error);
          if (!isCompleted) {
            finalResult = {
              error: "Failed to parse server response",
              message: "Invalid response format received from server",
              task_id: null
            };
            cleanup();
            abortController.abort();
            isCompleted = true;
            resolve(finalResult);
          }
        }
      },
      onerror(error) {
        console.error('SSE Error:', error);
        if (!isCompleted) {
          const errorResult = onError(error) || {
            error: "Connection error",
            message: "Failed to maintain connection with server",
            task_id: null
          };
          finalResult = errorResult;
          cleanup();
          abortController.abort();
          isCompleted = true;
          resolve(finalResult);
        }
        return 0; // Stop retrying
      },
      onclose: () => {
        
        onClose();
        if (!isCompleted) {
          cleanup();
          if (finalResult) {
            resolve(finalResult);
          } else {
            resolve({
              error: "Connection closed unexpectedly",
              message: "The connection was closed before completion",
              task_id: null
            });
          }
        }
      }
    }).catch((error) => {
      cleanup();
      if (error.name === 'AbortError') {
        resolve(finalResult || {
          error: "Request was cancelled",
          message: "The request was cancelled or timed out",
          task_id: null
        });
      } else {
        reject(error);
      }
    });
  });
}

// Check if the URL contains localhost or 127.0.0.1
let code_query_backend_base_url;

if (
  backend_base_url &&
  (backend_base_url.includes("localhost") ||
    backend_base_url.includes("127.0.0.1"))
) {
  // If the URL is a localhost URL, assign a different value for code_query_backend_base_url
  code_query_backend_base_url = process.env.NEXT_PUBLIC_CODEQUERY_API_URL;
} else {
  // For production environment, you might want to use the same URL or a different one
  code_query_backend_base_url = backend_base_url;
}

export const getHeadersRaw = () => {
  let idToken = Cookies.get("idToken");
  let is_public_selected = Cookies.get("is_public_selected");
  let selected_tenant_id = Cookies.get("selected_tenant_id");
  let selected_project_creator_email = Cookies.get(
    "selected_project_creator_email"
  );
  let retries = 3;

  return {
    "Content-Type": "application/json",
    Authorization: `Bearer ${idToken}`,
    is_public_selected: is_public_selected || "",
    selected_tenant_id: selected_tenant_id || "",
    selected_project_creator_email: selected_project_creator_email || "",
  };
};

export const getHeaders = async () => {
  let idToken = Cookies.get("idToken");
  const refreshToken = Cookies.get("refreshToken");
  const tenant_id = Cookies.get("tenant_id");
  
  // Check if idToken is expired or not available but refreshToken exists
  if ((!idToken || isTokenExpired(idToken)) && refreshToken && tenant_id) {
    try {
      console.log('Token expired or missing, attempting refresh...');
      await refreshAccessToken(refreshToken, tenant_id);
      // Token should be updated in cookies by refreshAccessToken
      idToken = Cookies.get("idToken");
      console.log('Token refreshed successfully');
    } catch (error) {
      console.error('Failed to refresh token:', error);
      // Clean up invalid tokens
      removeCookie("idToken");
      removeCookie("refreshToken");
      removeCookie("userId");
      removeCookie("username");
      removeCookie("email");
      
      // Throw error to indicate authentication failure
      throw new Error("Authentication required - please login again");
    }
  }
  
  const headers = new Headers(getHeadersRaw());
  return headers;
};

const LOGGING_LEVEL = 1;

export const fetchProjectsList = async (
  page = 1,
  pageSize = 10,
  search = "",
  creatorFilter = ""
) => {
  const base_url = backend_base_url; // Replace with your actual backend URL

  try {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
    });

    if (search && search.trim()) {
      params.append("search", search.trim());
    }

    if (creatorFilter && creatorFilter.trim()) {
      params.append("creator_filter", creatorFilter.trim());
    }
    const response = await fetch(`${base_url}/node/list_projects/?${params}`, {
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Error fetching projects: ${response.status}`);
    }

    const projectsData = await response.json();
    return projectsData;
  } catch (error) {
    throw error; // Re-throw the error for handling at a higher level
  }
};

export const fetchProjectCreators = async () => {
  const base_url = backend_base_url;

  try {
    const response = await fetch(`${base_url}/node/project-created-users/`, {
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Error fetching creators: ${response.status}`);
    }

    const result = await response.json();

    if (result.success && result.data) {
      return result.data.creators || [];
    }

    return [];
  } catch (error) {
    console.error("Error in fetchProjectCreators:", error);
    throw error;
  }
};

export const createProjectGuidanceFlow = async (projectId, data) => {
  const base_url = backend_base_url;

  try {
    // Ensure projectId is an integer as the backend expects it
    if (typeof projectId === "string") {
      projectId = parseInt(projectId);
    }

    // Send data directly without wrapping it in another structure
    const response = await fetch(`${base_url}/project/guidance-flow`, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify(data), // Send the data directly
    });

    if (!response.ok) {
      throw new Error(
        `Failed to create project guidance flow: ${response.status} ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

export const listAllUsers = async (limit = 60) => {
  const base_url = backend_base_url;
  const headers = await getHeaders();

  const response = await fetch(`${base_url}/users/?limit=${limit}`, {
    method: "GET",
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error("Network response was not ok");
  }

  const data = await response.json();
  // Ensure data is an array

  return data;
};

export async function checkUserActive(userId = null) {
  try {
    let url = `${backend_base_url}/users/active/`;
    if (userId) {
      url = `${url}?user_id=${userId}`;
    }
    const response = await fetch(`${backend_base_url}/users/active/`, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to check user active status");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

export const connectUsersToDiscussion = async (projectId, members) => {
  const base_url = backend_base_url;
  const headers = await getHeaders();

  const payload = {
    project_update: {
      members: members,
    },
  };

  const response = await fetch(`${base_url}/users/connect_to/${projectId}/`, {
    method: "PATCH",
    headers: await getHeaders(),
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    throw new Error("Network response was not ok");
  }

  const responseData = await response.json();

  return responseData;
};
export const getUsersInDiscussion = async (projectId) => {
  const base_url = backend_base_url;
  const headers = await getHeaders();

  const response = await fetch(
    `${base_url}/users/project/?project_id=${projectId}`,
    {
      method: "GET",
      headers: await getHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error("Network response was not ok");
  }

  return response.json();
};

// ---------- USER MANAGEMENT API --------------------------------

// utils/api.js
export async function getTenantName(encrypted_tenant_id) {
  const url = `${backend_base_url}/auth/get_organization_name?tenant_id=${encodeURIComponent(
    encrypted_tenant_id
  )}`;
  const response = await fetch(url, {
    headers: await getHeaders(),
  });
  return response.json();
}

export async function getOrganizationNameById(tenant_id) {
  const url = `${backend_base_url}/auth/get_organization_name_by_id?tenant_id=${encodeURIComponent(
    tenant_id
  )}`;
  try {
    const response = await fetch(url, {
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to fetch organization name");
    }

    return response.json();
  } catch (error) {
    throw error;
  }
}

export async function loginUser(tenant_id, email, password) {
  const url = `${backend_base_url}/auth/login`;

  const body = JSON.stringify({
    email: email,
    password: password,
    organization_id: encodeURIComponent(tenant_id),
  });

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: body,
    });

    const data = await response.json(); // Moved outside the check to inspect the response details

    if (!response.ok) {
      const errorMessage = data?.detail || "Login failed.";
      return { status: response.status, error: true, message: errorMessage };
    }

    // 30-day expiration for all authentication cookies
    const cookieOptions = { expires: 30 }; // 30 days
    const idTokenOptions = { expires: 1 }; // 1 day for idToken

    await setCookie("idToken", data["id_token"], idTokenOptions);
    await setCookie("refreshToken", data["refresh_token"], cookieOptions);
    let claims = await decrypt(data["id_token"]);
    await setCookie(
      "encrypted_tenant_id",
      encryptTenantId(data["tenant_id"]),
      cookieOptions
    );
    await setCookie("tenant_id", data["tenant_id"], cookieOptions);
    await setCookie("userId", claims["sub"], cookieOptions);
    await setCookie("username", claims["custom:Name"], cookieOptions);
    await setCookie("email", claims["email"], cookieOptions);

    return {
      status: response.status,
      error: false,
      message: "Login successful.",
    };
  } catch (error) {
    return { status: 500, error: true, message: "An error occurred." };
  }
}

export async function getUserOrganizations(email) {
  if (!email) {
    throw new Error("Email is required");
  }

  // Construct the URL with proper URL encoding for the query parameter
  const url = `${backend_base_url}/auth/user-organizations?email=${encodeURIComponent(
    email
  )}`;

  // Create proper headers structure
  const headers = {
    Accept: "application/json",
    ...(await getHeaders()),
  };

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: headers,
    });

    // Check if the response is OK (status code 200-299)
    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.detail || `API request failed with status ${response.status}`
      );
    }

    return await response.json();
  } catch (error) {
    // Enhance error with context
    if (error.message === "Failed to fetch") {
      throw new Error(
        "Network error. Please check your connection and try again."
      );
    }
    throw error;
  }
}

export async function refreshAccessToken(refreshToken, tenant_id) {
  const encryptedTenantId = encryptTenantId(tenant_id);
  const url = `${backend_base_url}/auth/refresh_token?tenant_id=${encryptedTenantId}`;

  const body = JSON.stringify({
    refresh_token: refreshToken,
  });

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        accept: "application/json",
        "Content-Type": "application/json",
      },
      body: body,
    });

    if (!response.ok) {
      throw new Error(`HTTP Response Status: ${response.status}`);
    }

    const data = await response.json();

    // Set new id token to cookies like login with 30-day expiration
    const cookieOptions = { expires: 30 }; // 30 days
    const idTokenOptions = { expires: 1 }; // 1 day for idToken

    await setCookie("idToken", data["id_token"], idTokenOptions);
    
    // Update refresh token if provided in response
    if (data["refresh_token"]) {
      await setCookie("refreshToken", data["refresh_token"], cookieOptions);
    }
    
    let claims = await decrypt(data["id_token"]);
    await setCookie("userId", claims["sub"], cookieOptions);
    await setCookie("username", claims["custom:Name"], cookieOptions);
    await setCookie("email", claims["email"], cookieOptions);

    // Trigger page refresh after successful token refresh
    setTimeout(() => {
      if (typeof window !== 'undefined') {
        window.location.reload();
      }
    }, 100);

    return data;
  } catch (error) {
    removeCookie("refreshToken");
    throw error;
  }
}

export async function signUpUser(
  email,
  password,
  name,
  designation,
  department,
  organization_id,
  has_accepted_terms,
  referral_code = null
) {
  const url = `${backend_base_url}/auth/signup`;

  const body = JSON.stringify({
    email: email,
    password: password,
    name: name,
    designation: designation,
    department: department,
    organization_id: organization_id,
    has_accepted_terms: has_accepted_terms,
    referral_code: referral_code,
  });

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: body,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.detail || `HTTP error status: ${response.status}`
      );
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
}

export async function forgotPasswordRequest(email, tenant_id) {
  const encodedEmail = encodeURIComponent(email);

  const url = `${backend_base_url}/auth/forgot_password?email=${encodedEmail}&tenant_id=${encodeURIComponent(
    tenant_id
  )}`;
  const headers = {
    accept: "application/json",
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

export async function confirmSignUp(
  username,
  confirmationCode,
  tenantId = null,
  referralCode = null
) {
  const url = `${backend_base_url}/auth/confirm_signup`;

  // Build query parameters
  const params = new URLSearchParams({
    username: username,
    confirmation_code: confirmationCode,
  });

  if (tenantId) {
    params.append("tenant_id", tenantId);
  }

  // ✅ ADD REFERRAL_CODE TO QUERY PARAMS IF AVAILABLE
  if (referralCode) {
    params.append('referral_code', referralCode);
    
  }

  try {
    const response = await fetch(`${url}?${params.toString()}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData?.detail || "Confirmation failed");
    }

    return await response.json();
  } catch (error) {
    console.error("Error confirming signup:", error);
    throw error;
  }
}

export async function confirmForgotPassword(
  tenant_id,
  email,
  password,
  confirmationCode
) {
  const url = `${backend_base_url}/auth/confirm_forgot_password?confirmation_code=${encodeURIComponent(
    confirmationCode
  )}`;

  const body = JSON.stringify({
    email: email,
    password: password,
    organization_id: tenant_id,
  });

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        accept: "application/json",
        // add other necessary headers here
      },
      body: body,
    });

    if (!response.ok) {
      throw new Error(`HTTP error status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

export async function resendConfirmationCode(username, tenant_id) {
  const url = `${backend_base_url}/auth/resend_confirmation_code?username=${encodeURIComponent(
    username
  )}&tenant_id=${encodeURIComponent(tenant_id)}`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: "", // Empty body as per the cURL command
    });

    if (!response.ok) {
      throw new Error(`HTTP error status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}
export const getUserById = async (userId) => {
  let base_url = backend_base_url;
  const response = await fetch(`${base_url}/users/${userId}`, {
    method: "GET",
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error("Network response was not ok");
  }

  return response.json();
};

export const getUser = async () => {
  try {
    const headers = await getHeaders();
    const response = await fetch(`${backend_base_url}/users/me/`, {
      headers: headers,
    });
    const data = await response.json();

    // Check if tenant status is inactive
    if (data.tenant_id != process.env.NEXT_PUBLIC_ROOT_TENANT_ID) {
      if (data.status !== "active") {
        return {
          error: true,
          errorType: "INACTIVE_TENANT",
          message: "Tenant is inactive",
        };
      }
    }
    // Check if tenant is not open to public
    // if (data.opentopublic !== "true") {
    //   return {
    //     error: true,
    //     errorType: "INACTIVE_TENANT",
    //     message: "Tenant is not open to public",
    //   };
    // }

    return data;
  } catch (error) {
    throw error; // Re-throw other errors
  }
};

// ---------- USER MANAGEMENT API --------------------------------

// List users APIs START -----------------------------------------------------------------

export async function listUsers() {
  let base_url = backend_base_url;

  const response = await fetch(`${base_url}/users/?limit=60`, {
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Fetching nodes failed");
  }
  return await response.json();
}

export async function getRecentProjectActivity() {
  let base_url = backend_base_url;

  const response = await fetch(`${base_url}/users/recent_projects/`, {
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Fetching recent activity failed");
  }
  return await response.json();
}

// requirement api

export async function getAvailableStatus() {
  let base_url = backend_base_url;
  // const header = await getHeaders();

  const response = await fetch(`${base_url}/requirement/available_statuses`, {
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Fetching available Status Failed");
  }

  const data = await response.json();

  return data;
}

export async function getAvailablePriorities() {
  let base_url = backend_base_url;

  const response = await fetch(`${base_url}/requirement/available_priorities`, {
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error("Fetching available Priorities Failed");
  }

  const data = await response.json();

  return data;
}

export async function updateNodeByPriority(
  nodeId,
  propertyName,
  propertyValue,
  session_id = null
) {
  const base_url = backend_base_url;

  const url = `${base_url}/node/property/${nodeId}`;

  const requestBody = {
    property_name: propertyName,
    property_value: propertyValue,
  };

  if (session_id) {
    requestBody.session_id = session_id;
  }

  const response = await fetch(url, {
    method: "PATCH",
    headers: await getHeaders(),
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    throw new Error("Updating node property failed");
  }

  const data = await response.json();
  return data;
}

export async function getProjectCounts(projectId, type) {
  let base_url = backend_base_url;

  const response = await fetch(
    `${base_url}/node/project_counts/${projectId}/${type}`,
    {
      headers: await getHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch project counts for ${type}`);
  }

  const data = await response.json();
  return data;
}

export async function getAssignedTasks() {
  let base_url = backend_base_url;

  const response = await fetch(`${base_url}/users/tasks/`, {
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Fetching assigned tasks failed");
  }
  return await response.json();
}

export async function assignTask(nodeid, userid) {
  let base_url = backend_base_url;
  const payload = {
    node_id: nodeid,
    user_id: userid,
  };

  const response = await fetch(`${base_url}/users/assign_task/`, {
    method: "PATCH",
    body: JSON.stringify(payload),
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error("Failed to update user profile");
  }

  return response.json();
}

export async function getCurrentDiscussions() {
  let base_url = backend_base_url;

  const response = await fetch(`${base_url}/users/discussions/`, {
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Fetching current discussions failed");
  }
  return await response.json();
}

export async function getProjectUserToAdd(projectId) {
  let base_url = backend_base_url;
  const url = `${base_url}/users/project_user_to_add/?project_id=${projectId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(), // Calling getHeaders to get the headers
    });

    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(
        errorData?.detail || "Failed to fetch project user to add"
      );
    }
  } catch (error) {
    throw error;
  }
}

export async function getUsersToAddToDiscussion(discussionId) {
  let base_url = backend_base_url;
  const url = `${base_url}/discussion/${discussionId}/get_users_to_add`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(), // Calling getHeaders to get the headers
    });

    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(
        errorData?.detail || "Failed to fetch users to add to discussion"
      );
    }
  } catch (error) {
    throw error;
  }
}

export const getDiscussion = async (discussionId) => {
  let base_url = backend_base_url;
  const url = `${base_url}/discussion/${discussionId}/get_discussion`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
};

// utils/api.js
export async function editUserProfile(userIdVal, updatedAttributes) {
  const base_url = backend_base_url;
  const payload = {
    user_attributes: updatedAttributes,
  };
  let header = await getHeaders();
  const response = await fetch(`${base_url}/users/${userIdVal}`, {
    method: "PATCH",
    body: JSON.stringify(payload),
    headers: header,
  });

  if (!response.ok) {
    throw new Error("Failed to update user profile");
  }

  return response.json();
}

export const addUserToDiscussion = async (discId, userIds) => {
  const url = `${backend_base_url}/discussion/${discId}/add_users`;
  const data = userIds.map((userId) => ({ user_id: userId }));

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP error status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    throw error;
  }
};

export const fetchUsersInDiscussion = async (discussionId) => {
  const base_url = backend_base_url;

  const response = await fetch(
    `${base_url}/discussion/${discussionId}/get_users`,
    {
      method: "GET",
      headers: await getHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error(`HTTP error status: ${response.status}`);
  }

  const data = await response.json();
  return data;
};

export async function fetchNodeById(nodeId, nodeType) {
  // Input validation
  if (!nodeId) {
    throw new Error("Node ID is required");
  }
  if (!nodeType) {
    throw new Error("Node type is required");
  }

  let base_url = backend_base_url;

  const buildUrl = (nt) => `${base_url}/node/${nodeId}?node_type=${encodeURIComponent(nt)}`;
  const tryCases = [nodeType];
  // Add common case alternatives to mitigate backend case sensitivity
  if (typeof nodeType === 'string') {
    const lower = nodeType.toLowerCase();
    const upperFirst = nodeType.charAt(0).toUpperCase() + nodeType.slice(1).toLowerCase();
    if (!tryCases.includes(lower)) tryCases.push(lower);
    if (!tryCases.includes(upperFirst)) tryCases.push(upperFirst);
    const upper = nodeType.toUpperCase();
    if (!tryCases.includes(upper)) tryCases.push(upper);
  }

  try {
    let lastResponse = null;
    let response = null;
    for (const nt of tryCases) {
      response = await fetch(buildUrl(nt), {
        headers: await getHeaders(),
      });
      lastResponse = response;
      // If ok or a non-500 error, break; otherwise try next casing
      if (response.ok || response.status !== 500) break;
    }

    if (!response.ok) {
      let errorMessage = `Fetching node failed: ${response.status} ${response.statusText}`;

      try {
        // Try to parse as JSON first (most API errors)
        const errorBody = await response.json();
        if (errorBody.detail) {
          errorMessage += ` - ${errorBody.detail}`;
        } else if (errorBody.message) {
          errorMessage += ` - ${errorBody.message}`;
        } else if (typeof errorBody === "string") {
          errorMessage += ` - ${errorBody}`;
        }
      } catch (parseError) {
        // If JSON parsing fails, try as text
        try {
          const errorText = await response.text();
          if (errorText) {
            errorMessage += ` - ${errorText}`;
          }
        } catch (textError) {
          // If both fail, just use the basic error message
          console.warn("Could not parse error response:", textError);
        }
      }

      // Handle specific status codes
      if (response.status === 422) {
        throw new Error(`Validation error: ${errorMessage}`);
      } else if (response.status === 404) {
        throw new Error(`Node not found: ${errorMessage}`);
      } else if (response.status === 403) {
        throw new Error(`Access denied: ${errorMessage}`);
      } else if (response.status === 401) {
        throw new Error(`Authentication required: ${errorMessage}`);
      }

      throw new Error(errorMessage);
    }

    const node = await response.json();

    if (!node || Object.keys(node).length === 0) {
      return null;
    }

    return node;
  } catch (error) {
    // Add context to the error if it's a network error
    if (error.message === "Failed to fetch") {
      throw new Error(
        `Network error: Unable to fetch node. Please check your connection and try again.`
      );
    }

    // Re-throw the error with original message
    throw error;
  }
}

export async function fetchNodePropertiesById(nodeId, nodeType) {
  let base_url = backend_base_url;
  const url = `${base_url}/node/properties/${nodeId}?node_type=${nodeType}`;

  try {
    const response = await fetch(url, {
      headers: await getHeaders(),
    });

    // First try to parse the response as JSON, if possible
    let errorBody;
    try {
      errorBody = await response.json();
    } catch {
      errorBody = await response.text();
    }

    // Handle different response statuses
    if (!response.ok) {
      // Handle specific status codes
      if (response.status === 404) {
        return null; // Node not found
      }

      throw new Error(
        errorBody?.message ||
          `Failed to fetch node properties: ${response.status} ${response.statusText}`
      );
    }

    // Handle empty or invalid responses
    if (!errorBody || Object.keys(errorBody).length === 0) {
      return null;
    }

    return errorBody;
  } catch (error) {
    return null; // Return null instead of throwing to handle gracefully in UI
  }
}

export async function fetchNodeBasedOnDataModelById(nodeId, nodeType) {
  const base_url = backend_base_url; // Assuming this is defined elsewhere
  const url = `${base_url}/node/v2/data_model/${nodeId}?node_type=${encodeURIComponent(
    nodeType
  )}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(), // Assuming getHeaders() handles Authorization and other necessary headers
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        `Fetching node data failed: ${errorData.detail || response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function fetchGraphNodeById(nodeId) {
  let base_url = backend_base_url; // Ensure backend_base_url is defined or imported appropriately

  const response = await fetch(`${base_url}/graph/${nodeId}`, {
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error("Fetching node failed");
  }
  const node = await response.json();
  return node;
}

export async function getPastDiscussionById(nodeId, status = "active") {
  let base_url = backend_base_url; // Ensure backend_base_url is defined or imported appropriately

  const response = await fetch(
    `${base_url}/discussion/${nodeId}/?status=${status}`,
    {
      headers: await getHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error("Fetching node failed");
  }
  const node = await response.json();
  return node;
}

export async function getComponentDeployments(projectId, containerId) {
  const url = `${backend_base_url}/architecture/component_deployments/${projectId}/${containerId}`;

  const response = await fetch(url, {
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error("Failed to fetch component deployments");
  }

  return response.json();
}

export async function getDeploymentForm(projectId, containerId) {
  const url = `${backend_base_url}/auth/deployment/${projectId}/${containerId}/get_deployment_form`;

  const response = await fetch(url, {
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error("Failed to fetch deployment form");
  }

  return response.json();
}

export async function updateDeploymentConfig(projectId, containerId, data) {
  const url = `${backend_base_url}/auth/deployment/${projectId}/${containerId}/update_deployment_config`;

  const response = await fetch(url, {
    method: "POST",
    headers: {
      ...(await getHeaders()),
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error("Failed to update deployment config");
  }

  const result = await response.json();

  return result;
}

export async function checkFigmaDesignForDeletion(
  figmaDesigns,
  tenant_id,
  project_id
) {
  const url = `${backend_base_url}/figma/check_design_in_db/${tenant_id}/${project_id}`;

  const response = await fetch(url, {
    method: "POST",
    headers: {
      ...getHeadersRaw(),
      "Content-Type": "application/json",
    },
    body: JSON.stringify(figmaDesigns),
  });

  if (response.status == 200) {
    const responseJson = await response.json();
    const responseData = responseJson.data;
    const updatedData = figmaDesigns.map((design) => {
      const filekey = design.file_key;

      if (responseData.some((data) => data[filekey] == true)) {
        return {
          ...design,
          deleted: true,
        };
      } else {
        return {
          ...design,
          deleted: false,
        };
      }
    });
    return updatedData;
  } else {
    throw new Error("Failed to fetch delete information");
  }
}

export async function listBranches(projectId, containerId) {
  const url = `${backend_base_url}/repository/list_branches/${projectId}/?container_id=${containerId}`;

  const response = await fetch(url, {
    method: "GET",
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error("Failed to list branches");
  }

  return response.json();
}

export async function fetchNodes(nodeType) {
  let base_url = backend_base_url;

  const response = await fetch(`${base_url}/node/?node_type=${nodeType}`, {
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Fetching nodes failed");
  }
  return await response.json();
}

export async function getTopLevelRequirements(projectId, nodeType = null) {
  let base_url = backend_base_url;

  const response = await fetch(
    `${base_url}/requirement/top_level_requirements/?root_node_id=${projectId}`,
    {
      headers: await getHeaders(),
    }
  );
  if (!response.ok) {
    throw new Error("Fetching requirements failed");
  }
  return await response.json();
}

export async function getProjectLLMCosts(projectId, tenantId) {
  if (!projectId || !tenantId) {
    throw new Error("Project ID is required");
  }

  let base_url = backend_base_url;
  const url = `${base_url}/users/llm_costs/${projectId}?tenant_id=${tenantId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch project costs: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getReconfigStatus(projectId) {
  if (!projectId) {
    throw new Error("Project ID is required");
  }
  let base_url = backend_base_url;
  const url = `${base_url}/discussion/reconfig/${projectId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch project costs: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function approveReconfiguration(projectId, currentTaskId) {
  if (!projectId) {
    throw new Error("Project ID not found.");
  }

  let base_url = backend_base_url;
  const url = `${base_url}/discussion/approve_reconfiguration/${projectId}?task_id=${currentTaskId}`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to approve reconfiguration:", response.status);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function createNewNode(
  nodeType,
  name,
  description,
  modelName,
  properties = {} // Add default empty object
) {
  let base_url = backend_base_url; // Ensure this variable is defined correctly

  try {
    const payload = {
      node_type: nodeType,
      name: name,
      description: description,
      model_name: modelName,
    };

    // Only add properties if they exist and repository_details exists and is not empty
    if (
      properties &&
      properties.repository_details &&
      properties.repository_details.length > 0
    ) {
      payload.properties = {
        repository_details: properties.repository_details.map((repo) => ({
          type: "github_repository",
          clone_url: repo.clone_url,
          branch_name: repo.branch_name,
          repo_name: repo.repo_name,
          description: repo.description,
          repo_id: repo.repo_id,
          repo_type: repo.repo_type,
          associated: repo.associated,
        })),
      };
    }

    const response = await fetch(`${base_url}/node/`, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`Failed to create node: ${response.statusText}`);
    }

    return await response.json(); // Parse and return the JSON response
  } catch (error) {
    throw error;
  }
}

export async function ingestLocalFiles(project_id, project_name, files) {
  try {
    const formData = new FormData();
    formData.append("project_id", project_id);
    formData.append("project_name", project_name);

    for (let file of files) {
      formData.append("files", file); // FastAPI collects this as a List[UploadFile]
    }

    const headers = getHeadersRaw();
    delete headers["Content-Type"];

    const response = await fetch(`${backend_base_url}/v1/ingest-local-files`, {
      method: "POST",
      headers: headers,
      body: formData,
    });

    if (response.ok) {
      return await response.json();
    }
  } catch (error) {
    throw error;
  }
}

export async function updateNodeById(nodeId, nodeType, properties) {
  let base_url = backend_base_url; // Ensure this variable is defined

  try {
    const response = await fetch(`${base_url}/node/${nodeId}`, {
      method: "PATCH",
      headers: await getHeaders(),
      body: JSON.stringify({ node_type: nodeType, properties: properties }),
    });

    // Return the entire response object for status inspection
    return response;
  } catch (error) {
    throw error;
  }
}

export async function createReferralCodeForUser(
  userId,
  forceRegenerate = false
) {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/admin/referral/create-code/${userId}`;

  const params = new URLSearchParams();
  if (forceRegenerate) {
    params.append("force_regenerate", "true");
  }

  const finalUrl = params.toString() ? `${url}?${params.toString()}` : url;

  try {
    const response = await fetch(finalUrl, {
      method: "POST",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData?.detail || "Failed to create referral code");
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating referral code:", error);
    throw error;
  }
}
export async function getUserReferralStats(userId) {
  let base_url = backend_base_url; // Ensure this variable is defined

  try {
    const response = await fetch(
      `${base_url}/auth/admin/referral/stats/${userId}`,
      {
        method: "GET",
        headers: await getHeaders(),
      }
    );

    const result = await response.json();

    if (response.ok) {
      return {
        success: true,
        data: result.data,
      };
    } else {
      throw new Error(result.message || "Failed to fetch referral stats");
    }
  } catch (error) {
    console.error("Error fetching referral stats:", error);
    throw error;
  }
}

export async function updateNodeProperties(nodeId, nodeType, properties) {
  const base_url = backend_base_url; // Ensure backend_base_url is defined or imported appropriately

  const url = `${base_url}/node/v2/${nodeId}?node_type=${encodeURIComponent(
    nodeType
  )}`;

  try {
    const response = await fetch(url, {
      method: "PATCH",
      headers: await getHeaders(), // Assuming getHeaders() handles Authorization and other necessary headers
      // body: JSON.stringify(properties)
      body: JSON.stringify({
        node_type: nodeType,
        ...properties,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        `Updating node properties failed: ${
          errorData.detail || response.statusText
        }`
      );
    }

    const updatedNode = await response.json();

    return updatedNode;
  } catch (error) {
    throw error;
  }
}

export async function getRequirementById(requirementId, projectId) {
  let base_url = backend_base_url;
  const response = await fetch(
    `${base_url}/requirement/${requirementId}/?project_id=${projectId}`,
    {
      headers: await getHeaders(),
    }
  );
  if (!response.ok) {
    throw new Error(`HTTP error status: ${response.status}`);
  }
  return await response.json();
}

export async function deleteNodeById(nodeId, nodeType) {
  let base_url = backend_base_url;

  const response = await fetch(
    `${base_url}/node/${nodeId}?node_type=${nodeType}`,
    {
      method: "DELETE",
      headers: await getHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error("Deleting node failed");
  }
  return response;
}

export async function fetchChildRequirements(parentNodeId, childNodeType) {
  let base_url = backend_base_url;

  const response = await fetch(
    `${base_url}/requirement/child_requirements/?parent_node_id=${parentNodeId}&child_node_type=${childNodeType}`,
    {
      headers: await getHeaders(),
    }
  );
  if (!response.ok) {
    throw new Error(`HTTP error status: ${response.status}`);
  }
  return await response.json();
}

/**
 * Exports requirements to Excel for a project
 * @param {string|number} projectId - The ID of the project
 * @returns {Promise<Blob>} - Excel file blob for download
 */
export async function exportRequirementsToExcel(projectId) {
  let base_url = backend_base_url;
  const url = `${base_url}/discussion/export_requirements/${projectId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to export requirements to Excel: ${response.status}`
      );
    }

    return await response.blob();
  } catch (error) {
    throw error;
  }
}

export async function fetchTaskCounts(projectId) {
  let base_url = backend_base_url;
  const url = `${base_url}/requirement/tasks_count/${projectId}`;

  try {
    const response = await fetch(url, {
      headers: await getHeaders(), // Assuming this function retrieves your auth headers
    });

    if (!response.ok) {
      throw new Error(`HTTP error status: ${response.status}`);
    }

    const taskCounts = await response.json();
    return taskCounts;
  } catch (error) {
    throw error; // Rethrow the error for higher-level handling
  }
}

// https://kavia-ai-qa-load-balancer-1428819748.us-east-1.elb.amazonaws.com/api/node/get_child_nodes/?node_id=93&node_type=Project&child_node_type=Architecture

export async function fetchChildNodes(nodeId, nodeType, childNodeType) {
  let base_url = backend_base_url; // Make sure this is correctly defined
  const response = await fetch(
    `${base_url}/node/get_child_nodes/?node_id=${nodeId}&node_type=${nodeType}&child_node_type=${childNodeType}`,
    {
      headers: await getHeaders(),
    }
  );
  if (!response.ok) {
    throw new Error("Fetching child nodes failed");
  }
  return await response.json();
}

export async function fetchSimilarNodes(nodeId, maxResults = 10) {
  let base_url = backend_base_url;
  const response = await fetch(
    `${base_url}/node/get_similar_nodes/?node_id=${nodeId}&max_results=${maxResults}`,
    {
      headers: await getHeaders(),
    }
  );
  if (!response.ok) {
    throw new Error("Fetching similar nodes failed");
  }
  return await response.json();
}

export async function fetchConnectedNodes(nodeId, nodeType, linkedNodeType) {
  let base_url = backend_base_url;
  const response = await fetch(
    `${base_url}/node/get_connected_nodes/?node_id=${nodeId}&node_type=${nodeType}&linked_node_type=${linkedNodeType}`,
    {
      headers: await getHeaders(),
    }
  );
  if (!response.ok) {
    throw new Error("Fetching connected nodes failed");
  }
  return await response.json();
}

export async function fetchNodeTree(nodeId, nodeType) {
  let base_url = backend_base_url;
  const response = await fetch(
    `${base_url}/node/get_node_tree/?node_id=${nodeId}&node_type=${nodeType}`,
    {
      headers: await getHeaders(),
    }
  );
  if (!response.ok) {
    throw new Error("Fetching node tree failed");
  }
  return await response.json();
}

export async function createNodeAssociation(
  startNodeId,
  endNodeId,
  relationshipType
) {
  let base_url = backend_base_url;

  const response = await fetch(`${base_url}/node/create_node_association/`, {
    method: "POST",
    headers: await getHeaders(),
    body: JSON.stringify({
      start_node_id: startNodeId,
      end_node_id: endNodeId,
      relationship_type: relationshipType,
    }),
  });
  if (!response.ok) {
    throw new Error("Creating node association failed");
  }
  return await response.json();
}

export async function fetchAssociatedNodes(
  nodeId,
  nodeType,
  associatedNodeType,
  relationshipType
) {
  let endpoint = `${backend_base_url}/node/get_associated_nodes/?node_id=${nodeId}&node_type=${nodeType}&associated_node_type=${associatedNodeType}&relationship_type=${relationshipType}`;

  const response = await fetch(endpoint, {
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Fetching associated nodes failed");
  }
  return await response.json();
}

export async function fetchLikelyAssociatedNodes(
  nodeId,
  nodeType,
  associatedNodeType
) {
  let endpoint = `${backend_base_url}/node/get_likely_associated_nodes/?node_id=${nodeId}&node_type=${nodeType}&associated_node_type=${associatedNodeType}`;

  const response = await fetch(endpoint, {
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Fetching likely associated nodes failed");
  }
  return await response.json();
}

// Discussion APIs

export async function fetchDiscussion(discussionId) {
  const base_url = backend_base_url; // Ensure this variable is defined correctly

  const response = await fetch(
    `${base_url}/discussion/${discussionId}/get_discussion`,
    {
      method: "GET",
      headers: await getHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch discussion");
  }

  return await response.json();
}

export const getPastDiscussion = async (discussionId) => {
  let base_url = backend_base_url;
  const url = `${base_url}/discussion/${discussionId}/get_discussion`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
};

export async function createNewDiscussion(
  parentNodeId,
  parentNodeType,
  properties
) {
  let base_url = backend_base_url; // Make sure this is defined correctly

  const currentDate = new Date().toISOString().split("T")[0];
  const discussionTitle = `Discussion on ${currentDate}`;

  const response = await fetch(`${base_url}/discussion/start`, {
    // Updated endpoint
    method: "POST",
    headers: await getHeaders(),
    body: JSON.stringify({
      parent_id: parseInt(parentNodeId, 10),
      parent_node_type: parentNodeType,
      Title: discussionTitle,
      Description: properties.description || discussionTitle, // Assuming a description is part of properties or use the title as fallback
      discussion_type: properties.discussion_type, // Assuming discussion_type is a required field in properties
    }),
  });
  if (!response.ok) {
    throw new Error("Starting new discussion failed");
  }
  return await response.json();
}

export async function updateDiscussion(
  discussionId,
  parentNodeType,
  user_comment
) {
  let base_url = backend_base_url;

  const response = await fetch(`${base_url}/discussion/update`, {
    // Updated endpoint
    method: "POST",
    headers: await getHeaders(),
    body: JSON.stringify({
      discussion_id: parseInt(discussionId, 10),
      parent_node_type: parentNodeType,
      user_comment: user_comment,
    }),
  });
  if (!response.ok) {
    throw new Error("Updating discussion failed");
  }
  return await response.json();
}

export async function getCapturedItemsFromDiscussion(
  discussionId,
  parentNodeType
) {
  let base_url = backend_base_url;
  const response = await fetch(
    `${base_url}/discussion/get_captured_items/?discussion_id=${discussionId}&parent_type=${parentNodeType}`,
    {
      // Updated endpoint
      headers: await getHeaders(),
    }
  );
  if (!response.ok) {
    throw new Error("Fetching captured items failed");
  }
  return await response.json();
}

export async function acceptChangesFromDiscussion(
  discussionId,
  parentNodeType
) {
  let base_url = backend_base_url;

  const response = await fetch(`${base_url}/discussion/accept_changes`, {
    // Updated endpoint
    method: "POST",
    headers: await getHeaders(),
    body: JSON.stringify({
      discussion_id: parseInt(discussionId, 10),
      parent_node_type: parentNodeType,
    }),
  });
  if (!response.ok) {
    throw new Error("Accepting changes failed");
  }
  return response;
}

// Configure APIs

export async function configureNode({
  node_id,
  node_type,
  levels,
  discussion_id,
}) {
  let base_url = backend_base_url; // Ensure this variable is defined correctly
  let header = await getHeaders();

  const response = await fetch(`${base_url}/configure/${node_type}/`, {
    method: "POST",
    headers: header,
    body: JSON.stringify({
      node_id: node_id,
      levels: levels,
      discussion_id: discussion_id,
    }),
  });

  if (!response.ok) {
    throw new Error("Failed to configure node");
  }

  return await response.json();
}

export async function configureNodeWithAgent({
  node_id,
  node_type,
  user_level,
  configurations,
  project_id,
  type = "auto-config",
}) {
  let base_url = backend_base_url; // Ensure this variable is defined correctly
  let header = await getHeaders();
  let url = `${base_url}/v2/tasks/configure/${node_id}/${node_type}/?user_level=${user_level}&type=${type}`;
  if (project_id) {
    url += `&project_id=${project_id}`;
  }

  const requestBody = {
    ...configurations, // Merge configurations if they exist
  };

  const response = await fetch(url, {
    method: "POST",
    headers: header,
    body: JSON.stringify(configurations),
  });

  if (!response.ok) {
    throw new Error("Failed to configure requirement root");
  }

  return await response.json();
}

export async function retrieveTaskStatus(project_id, task_id) {
  let base_url = backend_base_url;
  const url = `${base_url}/v2/tasks/retrieve-task/?project_id=${project_id}&task_id=${task_id}`;
  const response = await fetch(url, {
    method: "GET",
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Failed to retrieve task status");
  }
  return await response.json();
}

export async function getTaskUpdates(taskId) {
  let base_url = backend_base_url;
  const url = `${base_url}/v2/tasks/configure/task_updates/${taskId}`;

  const headers = await getHeaders(); // Function to get headers

  const response = await fetch(baseUrl, {
    method: "GET",
    headers: headers,
  });

  if (!response.ok) {
    throw new Error("Failed to fetch task updates");
  }

  return await response.json();
}

export async function getActiveTasks(nodeId, nodeType) {
  let base_url = backend_base_url;
  let header = await getHeaders();

  let url = `${base_url}/v2/tasks/active_tasks/${nodeId}`;
  if (nodeType) {
    url += `?node_type=${nodeType}`;
  }

  const response = await fetch(url, {
    method: "GET",
    headers: header,
  });

  if (!response.ok) {
    throw new Error("Failed to fetch active tasks");
  }

  return await response.json();
}

// Batch routes

export const stopBatchTask = async ({
  taskId,
  projectId,
  control = "stop",
}) => {
  try {
    const base_url = backend_base_url;
    const endpoint = `/batch/control/${taskId}`;

    // Construct query parameters
    const queryParams = new URLSearchParams({
      control: control,
      project_id: projectId,
    });

    const url = `${base_url}${endpoint}?${queryParams}`;

    const response = await fetch(url, {
      method: "PATCH",
      headers: new Headers({
        ...getHeadersRaw(),
        "Bypass-Credit-Check": "true",
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.message ||
          `Failed to stop batch task. Status: ${response.status}`
      );
    }

    const data = await response.json();

    return data;
  } catch (error) {
    throw error;
  }
};

export const fetchActiveTask = async (projectId, architectureId = null) => {
  let base_url = backend_base_url; // Assuming `backend_base_url` is defined elsewhere
  const headers = await getHeaders(); // Fetch headers dynamically
  let url = `${base_url}/batch/get_active_task/${projectId}`;
  const response = await fetch(url, {
    method: "GET",
    headers: headers,
  });

  if (!response.ok) {
    throw new Error("Fetching Active Task Failed");
  }

  const data = await response.json();
  return data;
};
export const fetchTask = async (taskId) => {
  let base_url = backend_base_url; // Assuming `backend_base_url` is defined elsewhere
  const headers = await getHeaders(); // Fetch headers dynamically
  let url = `${base_url}/batch/task_status/${taskId}`;

  const response = await fetch(url, {
    method: "GET",
    headers: headers,
  });

  if (!response.ok) {
    throw new Error("Fetching Active Task Failed");
  }

  const data = await response.json();
  return data;
};

export async function deleteTask(taskId, deleteRecord = false) {
  let base_url = backend_base_url;
  let url = `${base_url}/v2/tasks/${taskId}`;

  if (deleteRecord) {
    url += "?delete_record=true";
  }

  const headers = await getHeaders(); // Function to get headers

  const response = await fetch(url, {
    method: "DELETE",
    headers: headers,
  });

  if (!response.ok) {
    throw new Error("Failed to delete task");
  }

  return await response.json();
}

export async function getUserConfigurationModules(userId) {
  const base_url = backend_base_url; // Ensure this variable is defined correctly
  const headers = await getHeaders();
  const url = `${base_url}/users/configuration/modules/?user_id=${userId}`;

  const response = await fetch(url, {
    method: "GET",
    headers: headers,
  });

  if (!response.ok) {
    throw new Error("Failed to fetch user configuration modules");
  }

  return await response.json();
}

export async function updateLLMConfigurationModule(
  moduleName,
  llmModel,
  temperature
) {
  const base_url = backend_base_url;
  const headers = await getHeaders();
  const url = `${base_url}/users/configuration/modules/${moduleName}/`;

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers: headers,
      body: JSON.stringify({
        llm_model: llmModel,
        temperature: temperature,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.detail || "Failed to update LLM configuration module"
      );
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function fetchTaskUpdates(taskId) {
  let base_url = backend_base_url;
  let header = await getHeaders();
  const url = `${base_url}/v2/tasks/configure/task_updates/${taskId}`;

  const response = await fetch(url, {
    method: "GET",
    headers: header,
  });

  if (!response.ok) {
    throw new Error("Failed to fetch task updates");
  }

  return await response.json();
}

export async function startDiscussion(
  nodeId,
  nodeType,
  discussionType = null,
  modelName
) {
  let pathname = null;
  if (typeof window !== "undefined") {
    pathname = window.location.pathname;
    // Use pathname here
  }

  let base_url = backend_base_url; // Ensure this variable is defined correctly
  nodeType = nodeType.toLowerCase();

  // Parse URL to check if discussionNodeType is present
  const urlParams = new URLSearchParams(window.location.search);
  const discussionNodeType = urlParams.get("discussion_node_type");

  console.error({ modelName });

  let url = `${base_url}/discussion/start/${nodeType}/?node_id=${nodeId}`;
  if (modelName) {
    url += `&model_name=${encodeURIComponent(modelName)}`;
  }
  // Check for specific conditions to set the discussionType
  const isArchitecturalRequirementPage = pathname.includes(
    "architecture-requirement"
  );
  const isHighLevelPage = pathname.includes("high-level");
  const isInterface = pathname.includes("interface");

  // Only add discussion_type to the URL if discussionNodeType is not 'component'
  if (!discussionNodeType || discussionNodeType !== "component") {
    if (isArchitecturalRequirementPage) {
      discussionType = "architecture_requirement";
    } else if (isHighLevelPage) {
      discussionType = "design_details";
    }

    if (discussionType) {
      url += `&discussion_type=${discussionType}`;
    }
  }

  const response = await fetch(url, {
    method: "POST",
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error("Starting discussion for work item failed");
  }

  return await response.json();
}

export async function uploadDocument(fileinfo) {
  const url = `${backend_base_url}/documentation/upload`;
  let headers = getHeadersRaw();
  delete headers["Content-Type"];

  try {
    const response = await fetch(url, {
      headers: headers,
      method: "POST",
      body: fileinfo,
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { message: "Upload Failed" };
    }
  } catch (error) {
    return { message: "Upload Failed" };
  }
}

export const fetchNotifications = async () => {
  let base_url = backend_base_url;
  let header = await getHeaders();
  const response = await fetch(`${base_url}/users/notifications/`, {
    headers: header,
  });
  if (!response.ok) {
    throw new Error("Fetching Notifications Failed");
  }
  let data = await response.json();
  return data;
};
export const fetchUnreadMessage = async () => {
  let base_url = backend_base_url;
  let header = await getHeaders();
  const response = await fetch(
    `${base_url}/users/notifications/unread_count/`,
    {
      headers: header,
    }
  );
  if (!response.ok) {
    throw new Error("Fetching Unread Messages Failed");
  }
  let data = await response.json();
  return data;
};

export const markNotificationAsRead = async (notification_id) => {
  let base_url = backend_base_url;
  let header = await getHeaders();
  const response = await fetch(
    `${base_url}/users/notifications/${notification_id}/`,
    {
      method: "PATCH",
      headers: header,
    }
  );
  if (!response.ok) {
    throw new Error("Mark Notifications as Read Failed");
  }

  return await response.json();
};

export const markAllNotificationAsRead = async () => {
  let base_url = backend_base_url;
  let header = await getHeaders();
  const response = await fetch(
    `${base_url}/users/notifications/mark_all_read/`,
    {
      method: "PATCH",
      headers: header,
    }
  );
  if (!response.ok) {
    throw new Error("Mark all as Read Failed ");
  }
  let data = await response.json();
  return data;
};

export async function deleteNotification(notificationId, userId) {
  let base_url = backend_base_url;
  const url = `${base_url}/users/notifications/${notificationId}/?user_id=${userId}`;
  const response = await fetch(url, {
    method: "DELETE",
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Deleting notification failed");
  }
  return await response.json();
}

export async function deleteAllNotification() {
  let base_url = backend_base_url;
  const url = `${base_url}/users/notifications/`;
  const response = await fetch(url, {
    method: "DELETE",
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Deleting all notifications failed");
  }
  return await response.json();
}

// ------------------------ Notification endpoints ---------------------------

export const getUserConfigurationDetails = async (userId, module_name) => {
  let base_url = backend_base_url;

  let url = `${base_url}/users/configuration/?&module=${module_name}`;
  const response = await fetch(url, {
    method: "GET",
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Get Configuration Details Failed");
  }
  return await response.json();
};

export const updateConfigurationDetails = async (
  userId,
  module_name,
  selectedTimezone
) => {
  let base_url = backend_base_url;
  let url = `${base_url}/users/configuration/?user_id=${userId}&module=${module_name}`;
  const response = await fetch(url, {
    method: "PUT",
    headers: await getHeaders(),
    body: JSON.stringify({
      timezone: selectedTimezone,
    }),
  });
  if (!response.ok) {
    throw new Error("Updating Configuration Details Failed");
  }
  return await response.json();
};
// --------------------- Configuration Endpoints ------------------------------------

export const deleteMultipleNodes = async (nodeIds, nodeType) => {
  let base_url = backend_base_url;
  const nodeIdsQuery = nodeIds.map((id) => `node_ids=${id}`).join("&");
  let url = `${base_url}/node/?${nodeIdsQuery}&node_type=${nodeType}`;
  const response = await fetch(url, {
    method: "DELETE",
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Deleting all Nodes Failed");
  }
  return await response.json();
};

export const createTask = async (taskData) => {
  let base_url = backend_base_url;
  let url = `${base_url}/node/v2/`;
  const response = await fetch(url, {
    method: "POST",
    headers: await getHeaders(),
    body: JSON.stringify(taskData),
  });
  if (!response.ok) {
    throw new Error("Creating task failed");
  }
  return await response.json();
};
// -----Requirements tab endpoints ---------

export const connectUserToProject = async (project_id, project_update) => {
  let base_url = backend_base_url;
  let header = await getHeaders();
  const response = await fetch(`${base_url}/users/connect_to/${project_id}/`, {
    method: "PATCH",
    headers: header,
    body: JSON.stringify(project_update),
  });
  if (!response.ok) {
    throw new Error("Connect User to Project Failed");
  }
  let data = response.json();
  return data;
};

export const fetchActiveDiscussion = async (discussionId) => {
  let base_url = backend_base_url;
  const url = `${base_url}/discussion/${discussionId}/?status=active`;
  const options = {
    method: "GET",
    headers: await getHeaders(),
    withCredentials: true,
  };

  try {
    const response = await fetch(url, options);
    if (!response.ok) {
      throw new Error(`HTTP error status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
};

export const fetchUsers = async (discussionId) => {
  let base_url = backend_base_url;
  const response = await fetch(
    `${base_url}/discussion/${discussionId}/get_users`,
    {
      headers: await getHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error("Fetching users failed");
  }

  return await response.json();
};

export async function executeStep(
  nodeType,
  nodeId,
  discussionId,
  stepName,
  discussionType = null
) {
  let base_url = backend_base_url; // Ensure this variable is defined correctly
  nodeType = nodeType.toLowerCase();
  const url = `${base_url}/discussion/execute/`;

  const options = {
    method: "POST",
    headers: await getHeaders(),
    body: JSON.stringify({
      node_type: nodeType,
      step_name: stepName,
      node_id: nodeId,
      discussion_id: discussionId,
      discussion_type: discussionType,
    }),
    withCredentials: true,
  };

  const response = await fetch(url, options);
  if (!response.ok) {
    throw new Error(`Executing step failed for step: ${stepName}`);
  }

  return await response.json();
}

export async function repeatStep(
  nodeType,
  nodeId,
  discussionId,
  modelName,
  stepName,
  userComment = null,
  modificationIndex = null,
  discussionType = null
) {
  let base_url = backend_base_url;
  nodeType = nodeType.toLowerCase();
  const url = `${base_url}/discussion/repeat/`;

  const requestBody = {
    node_type: nodeType,
    node_id: nodeId,
    step_name: stepName,
    discussion_id: discussionId,
    discussion_type: discussionType,
    model_name: modelName
  };

  if (userComment !== null) {
    requestBody.usercomment = userComment;
  }
  if (modificationIndex !== null) {
    requestBody.modification_index = modificationIndex;
  }

  const response = await fetch(url, {
    method: "POST",
    headers: await getHeaders(),
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    throw new Error("Failed to repeat step");
  }
  return await response.json();
}
export async function cancelTask(taskId) {
  let base_url = backend_base_url;

  const response = await fetch(`${base_url}/configure/cancel_task/${taskId}`, {
    method: "POST",
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Failed to cancel task");
  }
  return await response.json();
}

export async function getAvailableOptions(nodeType) {
  try {
    const response = await fetch(
      `${backend_base_url}/configure/options/${nodeType}`,
      {
        method: "GET",
        headers: await getHeaders(),
      }
    );

    if (!response.ok) {
      throw new Error("Fetching available options failed");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}
// Architecture APIs

// This will render all the functional and architectural requirementnode with its childnode (Userstory and Epic)

export async function fetchArchitecturalRequirementsWithRelatedUserStories(
  projectId
) {
  const base_url = backend_base_url;

  const url = `${base_url}/architecture/architectural_requirements_with_related_userstories/${projectId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}
// This will render the particular functional or architectural requirementnode with its childnode (Userstory and Epic) s

export async function fetchRelatedNodes(projectId, childId, childType) {
  const base_url = backend_base_url; // Ensure backend_base_url is defined or imported appropriately

  const url = `${base_url}/architecture/related_nodes/${projectId}/${childId}/${childType}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

export async function fetchRootArchitecture(projectId) {
  let base_url = backend_base_url;

  const response = await fetch(
    `${base_url}/architecture/root_architecture/${projectId}`,
    {
      headers: await getHeaders(),
    }
  );
  if (!response.ok) {
    throw new Error("Fetching root architecture failed");
  }
  return await response.json();
}

export async function fetchChildArchitecture(nodeId) {
  let base_url = backend_base_url;

  const response = await fetch(
    `${base_url}/architecture/child_architectures/${nodeId}`,
    {
      headers: await getHeaders(),
    }
  );
  if (!response.ok) {
    throw new Error("Fetching child architecture failed");
  }
  return await response.json();
}

// get the implements relationship from the architecture node

export async function fetchArchitectureRelationships(projectId, queryType) {
  let base_url = backend_base_url;
  const url = `${base_url}/architecture/architecture_relationships/${projectId}/${queryType}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(
        `Fetching architecture relationships failed: ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function fetchArchitectureInterfacesWith(nodeId) {
  let base_url = backend_base_url; // Assuming backend_base_url is defined globally or imported
  const url = `${base_url}/architecture/interfaces_with/${nodeId}`;

  const response = await fetch(url, {
    headers: await getHeaders(), // Assuming getHeaders() handles Authorization and other necessary headers
  });

  if (!response.ok) {
    throw new Error("Fetching architecture interfaces failed");
  }

  return await response.json();
}

export async function fetchArchitectureInterfacesWithList(nodeId) {
  let base_url = backend_base_url;
  const url = `${base_url}/architecture/interfaces_with_list/${nodeId}`;
  const response = await fetch(url, {
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Fetching architecture interfaces with list failed");
  }
  return await response.json();
}

export async function createInterfaceNodeForEdge(projectId, relationshipId) {
  let base_url = backend_base_url;
  const url = `${base_url}/architecture/interface/?project_id=${projectId}&relationship_id=${relationshipId}`;
  const response = await fetch(url, {
    method: "POST",
    headers: await getHeaders(),
    body: JSON.stringify({}),
  });
  if (!response.ok) {
    throw new Error("Failed to create interface node for edge");
  }
  return await response.json();
}

export async function getInterface(interfaceId) {
  let base_url = backend_base_url;
  const url = `${base_url}/architecture/interface/${interfaceId}`;
  const response = await fetch(url, {
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Fetching interfaces  failed");
  }
  return await response.json();
}

export async function validateReferralCode(referralCode) {
  const url = `${backend_base_url}/auth/referral/validate/${referralCode}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Validation failed");
    }

    return await response.json();
  } catch (error) {
    console.error("Error validating referral code:", error);
    throw error;
  }
}

export async function getDetailedReferralStats(userId) {
  const url = `${backend_base_url}/manage/super/admin/referral/detailed-stats/${userId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to fetch detailed referral stats");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching detailed referral stats:", error);
    throw error;
  }
}

export async function getReferralUsageHistory(userId, limit = 50, skip = 0) {
  const url = `${backend_base_url}/manage/super/admin/referral/usage-history/${userId}?limit=${limit}&skip=${skip}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to fetch referral usage history");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching referral usage history:", error);
    throw error;
  }
}

export async function storeReferralCode(email, referralCode) {
  const url = `${backend_base_url}/auth/store-referral-code`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify({
        email: email,
        referral_code: referralCode,
      }),
    });

    if (!response.ok) {
      throw new Error("Failed to store referral code");
    }

    return await response.json();
  } catch (error) {
    console.error("Error storing referral code:", error);
    throw error;
  }
}

export async function getContainerFunctionalRequirements(
  projectId,
  containerId
) {
  let base_url = backend_base_url;

  const response = await fetch(
    `${base_url}/architecture/container_functional_requirements/${projectId}/${containerId}`,
    {
      headers: await getHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch container functional requirements");
  }

  return response.json();
}

export async function getComponentFunctionalRequirements(
  projectId,
  componentId
) {
  let base_url = backend_base_url;

  const response = await fetch(
    `${base_url}/architecture/component_functional_requirements/${projectId}/${componentId}`,
    {
      headers: await getHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch component functional requirements");
  }

  return response.json();
}

export async function getArchitectureById(architectureId, projectId) {
  let base_url = backend_base_url;
  const url = `${base_url}/architecture/${architectureId}?project_id=${projectId}`;
  const response = await fetch(url, {
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Fetching architecture by id failed");
  }
  return await response.json();
}

export async function fetchArchitecturalElementByProperty(
  parentArchitectureId,
  propertyName,
  propertyValue
) {
  const base_url = backend_base_url; // Assuming you have this variable defined elsewhere

  const encodedPropertyValue = encodeURIComponent(
    JSON.stringify(propertyValue)
  ); // Handle JSON in query

  const url = `${base_url}/architecture/get_architectural_element_by_property/${parentArchitectureId}?property_name=${propertyName}&property_value=${propertyValue}`;

  const response = await fetch(url, {
    headers: await getHeaders(), // Assuming you have a helper function for headers
  });

  if (!response.ok) {
    const errorData = await response.json(); // Try to get error details from backend
    throw new Error(
      `Fetching architectural element failed: ${
        errorData.detail || response.statusText
      }`
    );
  }

  return await response.json();
}

export async function getArchitectureInterface(interfaceId) {
  const url = `${backend_base_url}/architecture/interface/${interfaceId}?is_relationship=true`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(), // Calling getHeaders to get the headers
    });

    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(
        errorData?.detail || "Failed to fetch architecture interfaces status"
      );
    }
  } catch (error) {
    throw error;
  }
}

export async function getArchitectureInterfacesStatus(projectId) {
  const url = `${backend_base_url}/architecture/interfaces/${projectId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(), // Calling getHeaders to get the headers
    });

    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(
        errorData?.detail || "Failed to fetch architecture interfaces status"
      );
    }
  } catch (error) {
    throw error;
  }
}

// GetUserProjectURL

export async function getUserProjectURL(projectId) {
  const base_url = backend_base_url;
  try {
    const headers = await getHeaders();

    const response = await fetch(
      `${base_url}/codeview/get_user_project_url_stream?project_id=${encodeURIComponent(
        projectId
      )}`,
      {
        method: "GET",
        headers: headers,
      }
    );

    // Return the entire response object for status inspection
    return response;
  } catch (error) {
    throw error;
  }
}

export async function stopUserProject(projectId) {
  const base_url = backend_base_url;

  try {
    const headers = await getHeaders();

    const response = await fetch(
      `${base_url}/codeview/stop_project?project_id=${encodeURIComponent(
        projectId
      )}`,
      {
        method: "DELETE",
        headers: headers,
      }
    );

    // Return the entire response object for status inspection
    return response;
  } catch (error) {
    throw error;
  }
}

export async function fetchChatHistory(id = null, projectID) {
  const base_url = backend_base_url; // Assuming you have this variable defined elsewhere
  let url;

  if (id) {
    url = `${base_url}/conversation/${id}`;
  } else if (projectID !== null && projectID !== undefined) {
    url = `${base_url}/conversation/get_chat_history?limit=10&project_id=${projectID}`;
  } else if (id == null && projectID == undefined) {
    url = `${base_url}/conversation/get_chat_history?limit=10`;
  } else {
    throw new Error("Either id or projectID must be provided");
  }

  const response = await fetch(url, {
    headers: await getHeaders(), // Assuming you have a helper function for headers
  });

  if (!response.ok) {
    const errorData = await response.json(); // Try to get error details from backend
    throw new Error(
      `Fetching chat history element failed: ${
        errorData.detail || response.statusText
      }`
    );
  }

  return await response.json();
}

// Code Generation front-end APIs
// Create Repository for a Project
export async function createRepository(projectId) {
  const url = `${backend_base_url}/repository/create_repository/${projectId}/`;
  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
    });
    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.message || "Failed to create repository");
    }
  } catch (error) {
    throw error;
  }
}

// Get Repository Information
export async function getRepository(projectId) {
  const url = `${backend_base_url}/repository/get_repository/${projectId}/`;
  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
    });
    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.message || "Failed to create repository");
    }
  } catch (error) {
    throw error;
  }
}

// Filtered users on project

export async function filteredUsersProject(projectId) {
  const base_url = "http://localhost:8000/api/users/project_user_to_add/";
  const url = `${base_url}?project_id=${projectId}`;

  const response = await fetch(url, {
    method: "GET",
    headers: await getHeaders(),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(
      `Fetching project users to add failed: ${
        errorData.detail || response.statusText
      }`
    );
  }

  return await response.json();
}

// List All Repositories
export async function listRepositories() {
  const url = `${backend_base_url}/repository/list_repositories/`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.detail || "Failed to fetch repositories");
    }
  } catch (error) {
    throw error; // Handle or rethrow the error
  }
}

// Oauth login

export async function getOauthLoginAccess(scm_type) {
  const url = `${backend_base_url}/scm/oauth/${scm_type}/login?return_url=true`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });
    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.message || "Failed to get Oauth login access");
    }
  } catch (error) {
    throw error;
  }
}

// Oauth callback

export async function getOauthCallback(scm_type, state, code) {
  const url = `${backend_base_url}/scm/oauth/${scm_type}/callback?state=${state}&code=${code}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });
    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.message || "Failed to get Oauth callback");
    }
  } catch (error) {
    throw error;
  }
}

// Social login functions for Google and Microsoft (to be implemented)
export async function socialLogin(provider, tenant_id) {
  // This is a placeholder function for future implementation
  // provider can be 'google' or 'microsoft'

  // In the future, this will redirect to the appropriate OAuth endpoint

  // For now, we'll just return a mock response
  return {
    status: "info",
    message: `${provider} authentication coming soon!`,
  };
}

export async function googleCallback(code, state, tenant_id) {
  // Use a relative URL to leverage the Next.js proxy
  const apiPath = backend_base_url + "/auth/google/callback";
  const url = `${apiPath}?code=${encodeURIComponent(
    code
  )}&state=${encodeURIComponent(state)}${
    tenant_id ? `&tenant_id=${encodeURIComponent(tenant_id)}` : ""
  }`;

  // Add logging to debug the URL
  // Log the base URL for debugging

  try {
    // Use minimal headers for the initial authentication request
    // This avoids issues with missing tokens during the auth flow
    const headers = {
      "Content-Type": "application/json",
    };

    const response = await fetch(url, {
      method: "GET",
      headers: headers,
      // Add credentials to handle cookies properly
      // credentials: "include"
    });

    const data = await response.json();

    // If the response contains an error but is still a valid response (not a network error)
    if (!response.ok) {
      // Special handling for user_not_found error
      if (data.error === "user_not_found") {
        return {
          error: "user_not_found",
          message: data.message || "User does not exist. Please sign up first.",
          email: data.email || null, // Include email if available for pre-filling signup form
        };
      }

      throw new Error(
        data?.message ||
          `Failed to complete Google authentication (Status: ${response.status})`
      );
    }

    return data;
  } catch (error) {
    // Add more context to the error for debugging
    if (error.message === "Failed to fetch") {
      throw new Error(
        "Network error: Unable to connect to authentication server. Please check if the backend server is running."
      );
    }
    throw error;
  }
}

export async function handleSocialLoginCallback(
  provider,
  code,
  state,
  tenant_id
) {
  // This is a placeholder function for future implementation
  // Will handle the OAuth callback from Google or Microsoft

  // For now, we'll just return a mock response
  return {
    status: "info",
    message: `${provider} authentication callback handling coming soon!`,
  };
}

// scm model repository listing

export async function listRepository(service) {
  const url = `${backend_base_url}/repository/list_repositories/${service}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });
    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.message || "Failed to get all the repository");
    }
  } catch (error) {
    throw error;
  }
}

// get scm configurations

export async function getScmConfiguration() {
  const url = `${backend_base_url}/scm/configuration`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });
    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.message || "Failed to get scm configuration");
    }
  } catch (error) {
    throw error;
  }
}

// Get Work Items for a Project and Architecture
export async function getWorkItems(projectId, architectureId) {
  const url = `${backend_base_url}/v2/tasks/work_item/${projectId}/?architecture_id=${architectureId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.detail || "Failed to fetch work items");
    }
  } catch (error) {
    throw error;
  }
}

// Start Code Generation for a Project
export async function startCodeGeneration(projectId, architecureId) {
  let url = `${backend_base_url}/batch/start_code_generation/${projectId}/`;
  if (architecureId) {
    url += `?architecture_id=${architecureId}`;
  }

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
    });

    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.detail || "Failed to start code generation");
    }
  } catch (error) {
    throw error;
  }
}

// Resume Code Generation for a Project
export async function resumeStartCodeGeneration(
  projectId,
  architectureId,
  resumeTaskId,
  containerIds = []
) {
  let url = `${backend_base_url}/batch/start_code_generation/${projectId}/`;

  // Build query parameters (excluding container_ids)
  const params = new URLSearchParams();
  if (architectureId) {
    params.append("architecture_id", architectureId);
  }
  params.append("resume", "true");
  params.append("resume_task_id", resumeTaskId);

  url += `?${params.toString()}`;

  // Prepare request body with container_ids array
  const requestBody = {
    request_timestamp: new Date().toISOString(),
  };
  if (containerIds && containerIds.length > 0) {
    requestBody.container_ids = containerIds;
  }

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: Object.keys(requestBody).length > 0 ? JSON.stringify(requestBody) : undefined,
    });

    if (response.ok) {
      // Check if response is SSE format
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("text/event-stream")) {
        // Handle SSE response - read the stream
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        let result = "";
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          result += decoder.decode(value, { stream: true });
        }

        // Parse all SSE messages and find the one with end: true and task_id
        const lines = result.split('\n');
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line.startsWith("data: ")) {
            try {
              const jsonData = line.substring(6); // Remove 'data: ' prefix
              const parsedData = JSON.parse(jsonData);
              
              // Look for the final message with end: true AND task_id
              if (parsedData.end === true && parsedData.task_id) {
                return parsedData;
              }
            } catch (error) {
              console.warn("Failed to parse SSE line:", line, error);
              continue;
            }
          }
        }

        throw new Error("No available pods found");
      } else {
        // Regular JSON response
        return await response.json();
      }
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.detail || "Failed to resume code generation");
    }
  } catch (error) {
    console.error("Resume code generation error:", error);
    throw error;
  }
}

export async function startTestCodeGeneration(projectId, containerId) {
  let url = `${backend_base_url}/batch/start_code_generation/${projectId}/`;
  url += `?container_id=${containerId}&test_case=true`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
    });

    if (response.ok) {
      const contentType = response.headers.get("content-type");

      // Handle SSE response format
      if (contentType && contentType.includes("text/event-stream")) {
        const text = await response.text();
        // Extract and parse the JSON data from the SSE format
        const dataMatch = text.match(/data: ({.*})/);
        if (dataMatch && dataMatch[1]) {
          try {
            return JSON.parse(dataMatch[1]);
          } catch (e) {
            console.error("Failed to parse SSE data:", e);
            return text; // Return raw text if JSON parsing fails
          }
        }
        return text; // Return the raw text if no JSON match found
      }

      // Handle normal JSON response
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(
        errorData?.detail || "Failed to start test code generation"
      );
    }
  } catch (error) {
    console.error("Test code generation error:", error);
    throw error;
  }
}

// Test Task Trigger (Replace with actual functionality if needed)
export async function testTaskTrigger() {
  const url = `${backend_base_url}/v2/tasks/test/`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
    });

    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.detail || "Failed to start code generation");
    }
  } catch (error) {
    throw error;
  }
}

export async function cancelCodeGenerationTask(taskId) {
  const url = `${backend_base_url}/v2/tasks/${taskId}`;

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: await getHeaders(),
    });

    if (response.ok) {
      const taskData = await response.json();
      return { success: true, task: taskData };
    } else {
      const errorData = await response.json();
      const errorMessage = errorData?.detail || "Failed to cancel task";
      return { success: false, message: errorMessage };
    }
  } catch (error) {
    return {
      success: false,
      message: "An error occurred while canceling the task",
    };
  }
}

export async function getTaskById(taskId) {
  const url = `${backend_base_url}/v2/tasks/${taskId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.detail || "Failed to fetch task");
    }
  } catch (error) {
    throw error; // Handle or rethrow the error
  }
}

export async function fetchApiDocs(projectId) {
  const base_url = backend_base_url; // Replace with your actual backend URL

  const url = `${base_url}/architecture/api_docs/${projectId}`;

  try {
    const response = await fetch(url, {
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Error fetching API docs: ${response.status}`);
    }

    const apiDocs = await response.json();
    return apiDocs; // Assuming your API returns the docs in JSON format
  } catch (error) {
    throw error; // Rethrow the error for handling at a higher level
  }
}

export async function fetchSadDocumentation(projectId) {
  const base_url = backend_base_url; // Replace with your actual backend URL

  try {
    const response = await fetch(`${base_url}/architecture/sad/${projectId}`, {
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Error fetching SAD documentation: ${response.status}`);
    }

    const sadDocumentation = await response.json();
    return sadDocumentation;
  } catch (error) {
    throw error; // Re-throw the error for handling at a higher level
  }
}

// Get Detailed Report for a Task
export async function getDetailedReport(taskId) {
  const url = `${backend_base_url}/v2/tasks/get_detailed_report/${taskId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.detail || "Failed to fetch detailed report");
    }
  } catch (error) {
    throw error; // Handle or rethrow the error
  }
}
// GetArchitectural Requirement With Children

export async function getArchitecturalRequirementWithChildren(projectId) {
  const url = `${backend_base_url}/architecture/architecture_requirements/${projectId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(
        errorData?.detail || "Failed to fetch architectural requirement"
      );
    }
  } catch (error) {
    throw error;
  }
}

// Code view
export async function getCodeViewStatus(projectId) {
  const url = `${backend_base_url}/codeview/status?project_id=${projectId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(), // Calling getHeaders to get the headers
    });

    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.detail || "Failed to fetch code view status");
    }
  } catch (error) {
    throw error;
  }
}

//Get llm cost

//Get llm cost with month and year parameters
export async function getLLmCost(user_id, month, year) {
  const base_url = process.env.NEXT_PUBLIC_API_URL;
  const url = `${base_url}/users/llm_costs/?user_id=${user_id}&month=${month}&year=${year}`;
  let data;

  try {
    const response = await fetch(url, {
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in getting LLM cost");
    }

    data = await response.json();
  } catch (error) {
    data = null;
  }

  return data;
}

// Usage with month and year filters
export async function getFilteredLLMCost() {
  const user_id = Cookies.get("userId");
  if (!user_id) {
    return null;
  }

  const currentDate = new Date();
  const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-based
  const currentYear = currentDate.getFullYear();

  try {
    const data = await getLLmCost(user_id, currentMonth, currentYear);
    return data;
  } catch (error) {
    return null;
  }
}

export async function getInterfaceData(projectId) {
  let base_url = backend_base_url;
  const url = `${base_url}/architecture/interfaces/${projectId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(), // Using getHeaders for headers
    });

    if (!response.ok) {
      throw new Error("Failed to fetch interface data");
    }

    return await response.json();
  } catch (error) {
    throw error; // Re-throw the error after logging it
  }
}

export async function fetchSystemContextWithContainers(projectId) {
  let base_url = backend_base_url;
  const url = `${base_url}/architecture/system_context_with_containers/${projectId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in getting System context");
    }

    return await response.json();
  } catch (error) {}
}

export async function fetchBatchCallbackState(callbackName, jobId) {
  let base_url = backend_base_url;
  let header = await getHeaders();

  const url = `${base_url}/batch/callback_state/${callbackName}/${jobId}`;

  const response = await fetch(url, {
    method: "GET",
    headers: header,
  });

  if (!response.ok) {
    throw new Error("Failed to fetch batch callback state");
  }

  return await response.json();
}

export async function fetchBatchDocuments(task_id) {
  let base_url = backend_base_url;
  let header = await getHeaders();

  const url = `${base_url}/batch/get_deep_query_documents/${task_id}`;

  const response = await fetch(url, {
    method: "GET",
    headers: header,
  });

  if (!response.ok) {
    throw new Error("Failed to fetch documents");
  }

  return await response.json();
}

export async function fetchContainerWithComponent(projectId, containerId) {
  let base_url = backend_base_url;
  const url = `${base_url}/architecture/container_with_components/${projectId}/${containerId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in getting containers");
    }

    return await response.json();
  } catch (error) {}
}

export async function updateProjectNodeConfigurationStatus(projectData) {
  try {
    let base_url = backend_base_url;
    const response = await fetch(`${base_url}/project/node-configured`, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify(projectData),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to update project guidance flow: ${response.status} ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function fetchComponentsWithChildren(projectId, componentId) {
  let base_url = backend_base_url;
  const url = `${base_url}/architecture/component_with_associated_children/${projectId}/${componentId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in getting components");
    }
    return await response.json();
  } catch (error) {}
}

export async function updateProjectGuidanceFlow(
  projectId,
  stepName,
  status,
  data = {}
) {
  const base_url = backend_base_url;
  const url = `${base_url}/project/guidance-flow`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify({
        project_id: projectId,
        step_name: stepName,
        status: status,
        data: data,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Failed to update guidance flow: ${response.status} ${response.statusText} - ${errorText}`
      );
    }

    return await response.json();
  } catch (error) {
    throw error; // Re-throw to allow handling by the caller
  }
}

export async function fetchDesignWithChildren(
  projectId,
  componentId,
  designId
) {
  let base_url = backend_base_url;
  const url = `${base_url}/architecture/design_with_children/${projectId}/${componentId}/${designId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });
    if (!response.ok) {
      throw new Error("Error in getting design with children");
    }
    return await response.json();
  } catch (error) {}
}

export async function fetchContainerWithAllComponents(projectId) {
  let base_url = backend_base_url;
  const url = `${base_url}/architecture/container_with_all_components/${projectId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });
    if (!response.ok) {
      throw new Error("Error in getting all container children");
    }
    return await response.json();
  } catch (error) {}
}

export async function fetchComponentWithAllChildren(projectId) {
  let base_url = backend_base_url;
  const url = `${base_url}/architecture/component_with_associated_all_children/${projectId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });
    if (!response.ok) {
      throw new Error("Error in getting all components children");
    }
    return await response.json();
  } catch (error) {}
}

export async function getAllComponentsFromProject(projectId) {
  let base_url = backend_base_url;
  const url = `${base_url}/architecture/all_components_associated_with_project/${projectId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in getting all components");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function fetchComponentInterfaces(projectId, componentId) {
  let base_url = backend_base_url;
  const url = `${base_url}/architecture/component_interfaces/${projectId}/${componentId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in getting Component Interfaces");
    }

    return await response.json();
  } catch (error) {}
}

// END ---------------------------------------------------------------------------------------
// -----------------------------------------------ADMIN PANEL CHANGES-------------------------------------------------------------------------------------------------

export async function createOrganization(organizationData) {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/create_organization`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify({
        tenant_id: organizationData.tenant_username,
        name: organizationData.name,
        business_email: organizationData.business_email,
        industrial_type: organizationData.industrial_type,
        company_size: organizationData.company_size,
        domain: organizationData.domain,
        image: organizationData.image,
        plan_id: organizationData.plan_id,
        admin_name: organizationData.admin_name,
        admin_email: organizationData.admin_email,
        admin_contact_number: organizationData.admin_contact_number,
        admin_department: organizationData.admin_department,
        settings: {
          showfunctioncalling: false,
        },
        configurations: {
          max_users: organizationData.configurations.max_users,
          role_customization:
            organizationData.configurations.role_customization,
          api_access: organizationData.configurations.api_access,
          github_integration:
            organizationData.configurations.github_integration,
          jira_integration: organizationData.configurations.jira_integration,
          custom_reports: organizationData.configurations.custom_reports,
          export_capabilities:
            organizationData.configurations.export_capabilities,
        },
      }),
    });

    if (!response.ok) {
      throw new Error("Error in creating organization");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function checkOrganizationExists(tenantId) {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/is_organization_exists?tenant_id=${tenantId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error checking if organization exists");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function updateOrganizationStatus(organizationId, status) {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/update_organization_status?organization_id=${organizationId}&status=${status}`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in updating organization status");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function updateOrganizationPublicAccess(
  organizationId,
  openToPublic
) {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/update_organization_public_access?organization_id=${organizationId}&opentopublic=${openToPublic}`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in updating organization public access");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function updateOrganization(organizationData) {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/update_organization`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify(organizationData),
    });

    if (!response.ok) {
      throw new Error("Error in updating organization settings");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function fetchOrganizationPlans() {
  let base_url = backend_base_url;
  const url = `${base_url}/manage/super/plans`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in fetching organization plans");
    }

    return await response.json();
  } catch (error) {
    throw error; // Propagate error to the caller
  }
}

export async function getOrganizations(type) {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/organizations${
    type ? `?type=${type}` : ""
  }`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in getting organizations");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getOrganizationCosts() {
  const base_url = backend_base_url;
  const url = `${base_url}/users/organizations/costs`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in getting organization costs");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function fetchOrganization(orgId) {
  let base_url = backend_base_url;
  const url = `${base_url}/manage/org/${orgId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in fetching organization users");
    }

    return await response.json();
  } catch (error) {
    throw error; // Propagate error to the caller
  }
}

export async function fetchOrganizationUsers(orgId) {
  let base_url = backend_base_url;
  const url = `${base_url}/manage/org/${orgId}/users/`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in fetching organization users");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function fetchOrganizationGroups(orgId) {
  let base_url = backend_base_url;
  const url = `${base_url}/manage/org/${orgId}/groups/`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in fetching organization groups");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function fetchGroupDetails(orgId, groupId) {
  let base_url = backend_base_url;
  const url = `${base_url}/manage/org/${orgId}/groups/${groupId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in fetching group details");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getOrganizationCost(organizationId) {
  let base_url = backend_base_url;
  const url = `${base_url}/users/organizations/${organizationId}/costs`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to fetch organization cost: ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    throw new Error(`Error fetching organization cost: ${error.message}`);
  }
}

export async function getUserCost(organizationId, userId) {
  let base_url = backend_base_url;
  const url = `${base_url}/users/organizations/${organizationId}/users/${userId}/costs`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch user cost: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    throw new Error(`Error fetching user cost: ${error.message}`);
  }
}

export async function getOrganizationPackageCosts(organizationId) {
  let base_url = backend_base_url;
  const url = `${base_url}/users/organizations/${organizationId}/planCosts`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to fetch organization package costs: ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    throw new Error(
      `Error fetching organization package costs: ${error.message}`
    );
  }
}

export async function fetchGroupUsers(orgId, groupId) {
  let base_url = backend_base_url;
  const url = `${base_url}/manage/org/${orgId}/${groupId}/users/`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in fetching group users");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function createOrganizationGroup(orgId, payload) {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/org/${orgId}/groups/`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error("Error in creating organization group");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getOrganizationUser(organizationId, userId) {
  const url = `${backend_base_url}/manage/org/${organizationId}/users/${userId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error getting organization user");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getAllOrganizationsCosts() {
  const url = `${backend_base_url}/users/organizations/costs`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error getting organizations costs");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function addOrganizationUser(organizationId, userData) {
  const url = `${backend_base_url}/manage/org/${organizationId}/users/`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      if (errorData.detail) {
        if (errorData.detail.includes("User exists")) {
          return { error: errorData.detail }; // Return the full error message
        }
        return { error: errorData.detail }; // Return any other error details
      }
      return { error: "Error adding organization user" };
    }

    return await response.json();
  } catch (error) {
    return { error: error.message || "Error adding organization user" };
  }
}

export async function promoteUserToAdmin(organizationId, userId) {
  const url = `${backend_base_url}/manage/org/${organizationId}/users/${userId}/promote-to-admin`;

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error promoting user to admin");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function demoteUserFromAdmin(organizationId, userId) {
  const url = `${backend_base_url}/manage/org/${organizationId}/users/${userId}/demote-from-admin`;

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error demoting user from admin");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function addAdminUserToOrganization(data) {
  const url = `${backend_base_url}/manage/super/add_admin_user_to_organization`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify({
        organization_id: data.organization_id,
        name: data.name,
        email: data.email,
        contact_number: data.contact_number,
        department: data.department,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      const error = new Error(
        errorData.detail || "Error adding admin user to organization"
      );
      error.status = response.status;
      error.response = {
        status: response.status,
        data: errorData,
      };
      throw error;
    }

    return await response.json();
  } catch (error) {
    // If error already has response info, preserve it
    if (error.response || error.status) {
      throw error;
    }

    // For network errors, throw as-is
    throw error;
  }
}

export async function addGroupUsers(orgId, groupId, userId) {
  const url = `${backend_base_url}/manage/org/${orgId}/add_user_to_group?group_id=${groupId}&user_id=${userId}`;

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error adding user to group");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function addUsersToGroup(orgId, groupId, userIds) {
  // Remove the extra group_id parameter from the URL
  const url = `${backend_base_url}/manage/org/${orgId}/add_users_to_group?group_id=${
    groupId.split("?")[0]
  }`;

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers: await getHeaders(),
      body: JSON.stringify(userIds),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.detail || "Error adding users to group");
    }

    return {
      success: true,
      message: data.message,
      successCount: data.success_count,
      failedUsers: data.failed_users,
    };
  } catch (error) {
    throw {
      success: false,
      message: error.message || "Failed to add users to group",
    };
  }
}

export async function deleteUser(orgId, userId) {
  const url = `${backend_base_url}/manage/org/${orgId}/users/${userId}`;
  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: await getHeaders(),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.detail || "Error deleting user");
    }

    return {
      success: true,
      message: data.message,
    };
  } catch (error) {
    throw {
      success: false,
      message: error.message || "Failed to delete user",
    };
  }
}

export async function deleteGroup(orgId, groupId) {
  const url = `${backend_base_url}/manage/org/${orgId}/groups/${groupId}`;
  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: await getHeaders(),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.detail || "Error deleting group");
    }

    return {
      success: true,
      message: data.message,
    };
  } catch (error) {
    throw {
      success: false,
      message: error.message || "Failed to delete group",
    };
  }
}

export async function deleteOrganization(orgId) {
  const url = `${backend_base_url}/manage/super/organization/${orgId}`;
  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: await getHeaders(),
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function deleteAdminUserFromOrganization(userId) {
  const url = `${backend_base_url}/manage/super/delete_admin_user?user_id=${userId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    return response;
  } catch (error) {
    throw {
      success: false,
      message: error.message || "Failed to delete admin user",
    };
  }
}

export async function extractTextFromFile(projectId, files, callbackUrl = null) {
  const base_url = process.env.NEXT_PUBLIC_API_URL;
  const url = `${base_url}/file/extract_text?project_id=${projectId}`;

  try {
    // Create FormData
    const formData = new FormData();

    // Handle both single file and array of files
    const fileArray = Array.isArray(files) ? files : [files];

    // Append each file to formData
    fileArray.forEach((file) => {
      formData.append("files", file);
      formData.append("type", file.type);
    });

    // Add callback URL if provided
    if (callbackUrl) {
      formData.append('callback_url', callbackUrl);
    }

    // Get headers but remove Content-Type as it will be set automatically for FormData
    const headers = await getHeaders();
    headers.delete("Content-Type");

    const response = await fetch(url, {
      method: "POST",
      headers: headers,
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `Upload failed with status: ${response.status}`
      );
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}
// Helper function specifically for document uploads with callback
    export async function uploadDocumentWithCallback(projectId, file, taskId) {
    const tenantId = Cookies.get('tenant_id') || 'T0005';
    const callbackUrl = `http://localhost:8003/api/code_gen/sync-changes?tenant_id=${tenantId}&project_id=${projectId}&task_id=${taskId}`;
  
    return extractTextFromFile(projectId, file, callbackUrl);
}

export async function getProjectFiles(projectId) {
  const url = `${backend_base_url}/file/list-project-files/${projectId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw {
      success: false,
      message: error.message || "Failed to fetch project files",
    };
  }
}


export async function checkBootstrapStatus(projectId, supabaseProjectId) {
  const url = `${backend_base_url}/supabase/bootstrap-status/${supabaseProjectId}?project_id=${projectId}`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.detail || `Failed to check bootstrap status: ${response.status}`
      );
    }

    const result = await response.json();
    return result.bootstrap_status?.is_fully_bootstrapped || false;
  } catch (error) {
    console.error('Failed to check bootstrap status:', error);
    return false;
  }
}
export async function executeSQLViaLambda(projectId, sqlQuery) {
  const url = `${backend_base_url}/supabase/execute-sql-lambda`;
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({ project_id: projectId, sql_query: sqlQuery })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.detail || `Failed to execute SQL: ${response.status}`
      );
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}


export async function deleteProjectFile(projectId, file_uuid) {
  const url = `${backend_base_url}/file/delete-file`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify({
        project_id: projectId,
        file_uuid: file_uuid,
      }),
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw {
      success: false,
      message: error.message || "Failed to delete project file",
    };
  }
}

export async function getDashboardOverview(tenantId, days = 7) {
  if (!tenantId) {
    throw new Error("Tenant ID is required");
  }

  const base_url = backend_base_url;
  const url = `${base_url}/dashboard/overview/${tenantId}?days=${days}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch dashboard overview: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching dashboard overview:", error);
    throw error;
  }
}

export async function getAllTenants() {
  const base_url = backend_base_url;
  const url = `${base_url}/dashboard/tenants`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch tenants: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching tenants:", error);
    throw error;
  }
}

export async function getGeographicData(tenantId, days = 7) {
  if (!tenantId) {
    throw new Error("Tenant ID is required");
  }

  const base_url = backend_base_url;
  const url = `${base_url}/dashboard/geographic/${tenantId}?days=${days}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch geographic data: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching geographic data:", error);
    throw error;
  }
}

// ------SESSION TRACKER FOR SUPER ADMIN PORTAL --------------------------------------------------------------------------------

// In api.js, update the getOverallStats function
export async function getOverallStats(
  dateFrom = null,
  dateTo = null,
  status = null
) {
  const base_url = backend_base_url;
  let url = `${base_url}/manage/super/stats/overall`;

  const params = new URLSearchParams();

  if (dateFrom) {
    params.append("date_from", dateFrom);
  }

  if (dateTo) {
    params.append("date_to", dateTo);
  }

  if (status && status !== "all") {
    params.append("status", status);
  }

  if (params.toString()) {
    url += "?" + params.toString();
  }

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch overall stats: ${response.status}`);
    }

    const data = await response.json();
    return data.stats;
  } catch (error) {
    console.error("Error fetching overall stats:", error);
    throw error;
  }
}

// Add new function for chart data
export async function getChartData(
  dateFrom = null,
  dateTo = null,
  status = null,
  granularity = "day"
) {
  const base_url = backend_base_url;
  let url = `${base_url}/manage/super/stats/chart-data`;

  const params = new URLSearchParams();

  if (dateFrom) {
    params.append("date_from", dateFrom);
  }

  if (dateTo) {
    params.append("date_to", dateTo);
  }

  if (status && status !== "all") {
    params.append("status", status);
  }

  params.append("granularity", granularity);

  if (params.toString()) {
    url += "?" + params.toString();
  }

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch chart data: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching chart data:", error);
    throw error;
  }
}
// In api.js, make sure getTenants function is properly structured
export async function getTenants(
  dateFrom = null,
  dateTo = null,
  status = null
) {
  const base_url = backend_base_url;
  let url = `${base_url}/manage/super/tenants`;

  const params = new URLSearchParams();

  if (dateFrom) {
    params.append("date_from", dateFrom);
  }

  if (dateTo) {
    params.append("date_to", dateTo);
  }

  if (status && status !== "all") {
    params.append("status", status);
  }

  if (params.toString()) {
    url += "?" + params.toString();
  }

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch tenants: ${response.status}`);
    }

    const data = await response.json();

    return data;
  } catch (error) {
    console.error("Error fetching tenants:", error);
    throw error;
  }
}

export async function getTenantsValue(
  dateFrom = null,
  dateTo = null,
  status = null,
  service = null
) {
  const base_url = backend_base_url;
  let url = `${base_url}/manage/super/tenants_filter_service_type`;

  const params = new URLSearchParams();

  if (dateFrom) {
    params.append("date_from", dateFrom);
  }

  if (dateTo) {
    params.append("date_to", dateTo);
  }

  if (status && status !== "all") {
    params.append("status", status);
  }

  if (service && service !== "all") {
    params.append("service_type", service);
  }

  if (params.toString()) {
    url += "?" + params.toString();
  }

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch tenants: ${response.status}`);
    }

    const data = await response.json();

    return data;
  } catch (error) {
    console.error("Error fetching tenants:", error);
    throw error;
  }
}

export async function getUsersByTenant(tenantId, filters = {}) {
  if (!tenantId) {
    throw new Error("Tenant ID is required");
  }

  const base_url = backend_base_url;
  const params = new URLSearchParams();

  if (filters.dateFrom) params.append("date_from", filters.dateFrom);
  if (filters.dateTo) params.append("date_to", filters.dateTo);
  if (filters.status) params.append("status", filters.status);
  if (filters.service) params.append("service_type", filters.service);

  const url = `${base_url}/manage/super/tenants/${tenantId}/users${
    params.toString() ? "?" + params.toString() : ""
  }`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch users: ${response.status}`);
    }

    const data = await response.json();
    return data.users;
  } catch (error) {
    console.error("Error fetching users:", error);
    throw error;
  }
}

// utils/api.js or lib/api.js
export async function getSessionsByUser(
  tenantId,
  userId,
  dateFrom = null,
  dateTo = null,
  status = null,
  service = null,
  page = 1
) {
  if (!tenantId || !userId) {
    throw new Error("Tenant ID and User ID are required");
  }

  const base_url = backend_base_url;
  let url = `${base_url}/manage/super/tenants/${tenantId}/users/${userId}/sessions`;

  const params = new URLSearchParams();

  // Convert date strings to full ISO format if provided
  if (dateFrom) {
    // If dateFrom is just a date (YYYY-MM-DD), convert it to full ISO format
    const fromDate = dateFrom.includes("T")
      ? dateFrom
      : `${dateFrom}T00:00:00.000+0000`;
    params.append("date_from", fromDate);
  }

  if (dateTo) {
    // If dateTo is just a date (YYYY-MM-DD), convert it to full ISO format (end of day)
    const toDate = dateTo.includes("T")
      ? dateTo
      : `${dateTo}T23:59:59.999+0000`;
    params.append("date_to", toDate);
  }

  if (status) {
    params.append("status", status);
  }

  if (service) {
    params.append("service_type", service);
  }

  params.append("page", page.toString());

  if (params.toString()) {
    url += "?" + params.toString();
  }

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch sessions: ${response.status}`);
    }

    const data = await response.json();
    return data.sessions;
  } catch (error) {
    console.error("Error fetching sessions:", error);
    throw error;
  }
}

export async function getSessionDetails(taskId) {
  if (!taskId) {
    throw new Error("Task ID is required");
  }

  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/sessions/${taskId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch session details: ${response.status}`);
    }

    const data = await response.json();
    return data.session;
  } catch (error) {
    console.error("Error fetching session details:", error);
    throw error;
  }
}
// ----------------------------------------------------------------------------------------------------------------------------------------------//
// Removed duplicate renderHTML function and unused showdown configuration - use the one from helpers.js instead

// Common function
export const extractIpFromUrl = (url, param = "ip") => {
  try {
    const urlObj = new URL(url);
    const params = new URLSearchParams(urlObj.search);
    const ip = params.get(param);
    return ip || null;
  } catch (error) {
    return null;
  }
};

export async function initializeCodeQueryAPI(
  userId,
  tenantId,
  projectId,
  buildIds,
  session_name,
  description
) {
  const url = `${code_query_backend_base_url}/code_query/initialize_code_query`;
  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify({
        user_id: userId,
        tenant_id: tenantId,
        project_id: parseInt(projectId),
        build_ids: buildIds,
        session_name: session_name || "",
        description: description || "",
      }),
    });

    if (!response.ok) {
      const responseData = await response.json();
      if (responseData.detail?.includes("No such file")) {
        throw new Error("Failed to fetch the knowledge.");
      } else {
        throw new Error("Failed to initialize code query");
      }
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

export async function registerCodeQueryAgent(sessionId) {
  const url = `${code_query_backend_base_url}/code_query/register_agent/${sessionId}`;

  const response = await fetch(url, {
    method: "GET",
    headers: await getHeaders(),
  });

  if (response) {
    return await response.json();
  } else {
    return {
      status: "error",
      message: "Failed to register agent",
    };
  }
}

export async function checkCodeQueryStatusAPI(sessionId) {
  if (!sessionId) throw new Error("Session ID is required");

  const url = `${code_query_backend_base_url}/code_query/session-status/${sessionId}`;

  try {
    const response = await fetch(url, {
      withCredentials: true,
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to check session status");
    }

    return response;
  } catch (error) {
    throw error;
  }
}

export async function getCodeQueryLLMModels() {
  const url = `${code_query_backend_base_url}/code_query/get_available_code_query_models`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (response.ok) {
      const data = await response.json();
      return data.models;
    } else {
      throw new Error("Failed to fetch available models.");
    }
  } catch (error) {
    throw error;
  }
}

export async function getDiscussionLLMModels(projectId) {
  const url = `${backend_base_url}/discussion/get_available_discussion_models${
    projectId ? `?project_id=${projectId}` : ""
  }`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });
    if (response.ok) {
      const data = await response.json();
      return data;
    } else {
      throw new Error("Failed to fetch discussion models");
    }
  } catch (error) {
    throw error;
  }
}

// Add this function to your api.js file
export const deleteCodeQuerySessionAPI = async (
  sessionId,
  discussion_id,
  action
) => {
  const response = await fetch(
    `${code_query_backend_base_url}/code_query/delete-session/${sessionId}?discussion_id=${discussion_id}&action=${action}`,
    {
      method: "DELETE",
      headers: await getHeaders(),
      withCredentials: true,
    }
  );
  return response;
};

// Add this new function to check knowledge status
export async function checkKnowledgeStatusAPI(user_id) {
  const url = `${code_query_backend_base_url}/code_query/knowledge-status/${user_id}`;

  try {
    const response = await fetch(url, {
      headers: await getHeaders(),
      withCredentials: true,
    });

    if (!response.ok) {
      throw new Error("Failed to check knowledge status");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

// Add this new function
export async function fetchQueryHistory(projectId, limit = 20, skip = 0) {
  const url = `${backend_base_url}/conversation/get_query_history?limit=${limit}&project_id=${projectId}&skip=${skip}`;

  try {
    const response = await fetch(url, {
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to fetch query history");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

//function to delete code query branch
export async function deleteRepoBranch(buildId, projectId, repoService) {
  const url = `${code_query_backend_base_url}/code_query/delete-kg-repository?build_id=${buildId}&project_id=${projectId}&repo_service=${repoService}`;

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to delete branch.");
    }

    return await response.json();
  } catch (error) {}
}

export async function getStripeBillingPortal(email) {
  let base_url = backend_base_url;
  const encodedEmail = encodeURIComponent(email);
  const url = `${base_url}/payment/api/stripe/billing-portal/${encodedEmail}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to fetch billing portal data: ${response.status} - ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    throw error; // Re-throw the error after logging it
  }
}

// Add this new function
export async function deleteQueryDiscussion(discussionId) {
  const url = `${backend_base_url}/conversation/delete_query_discussion?discussion_id=${discussionId}`;

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to delete query discussion");
    }

    return await response.json();
  } catch (error) {
    // throw error;
    return false;
  }
}

// Profile picture upload function
export async function uploadProfilePicture(file) {
  const url = `${backend_base_url}/users/profile-picture`;

  try {
    // Create FormData instance

    const formData = new FormData();
    formData.append("file", file);

    // Get headers but remove Content-Type as FormData sets it automatically
    const headers = await getHeaders();
    headers.delete("Content-Type");

    const response = await fetch(url, {
      method: "POST",
      headers: headers,
      body: formData,
    });

    // Handle specific error status codes
    if (response.status === 422) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Invalid file format or size");
    }

    if (!response.ok) {
      throw new Error(`Upload failed with status: ${response.status}`);
    }

    // Return the response data which should contain the profile picture URL
    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

export async function getProfilePicture(userId) {
  const url = `${backend_base_url}/users/profile-picture?user_id=${userId}`;

  try {
    const headers = await getHeaders();
    const response = await fetch(url, {
      method: "GET",
      headers: headers,
    });

    if (!response.ok) {
      throw new Error(`Failed to get profile picture: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

export async function deleteProfilePicture(userId) {
  const url = `${backend_base_url}/users/${userId}/profile-picture`;

  try {
    const headers = await getHeaders();
    const response = await fetch(url, {
      method: "DELETE",
      headers: headers,
    });

    if (!response.ok) {
      throw new Error(`Failed to delete profile picture: ${response.status}`);
    }

    return true;
  } catch (error) {
    throw error;
  }
}

//  documentation related api

export async function addDocuments(project_id, file) {
  const url = `${backend_base_url}/file/extract_text?project_id=${project_id}`;
  const formData = new FormData();
  formData.append("file", file);
  formData.append("type", "application/pdf");

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: formData,
    });

    if (!response.ok) {
      throw new Error("Failed to upload the documents");
    }

    return await response.json();
  } catch (error) {
    // throw error;
    return false;
  }
}

export async function checkLiveStatus(project_id, ipAddress, architectureId) {
  const url = `${backend_base_url}/batch/check_live_status/?project_id=${project_id}&ip_address=${ipAddress}&architecture_id=${architectureId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to check live status");
    }

    // Handle streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = "";
    let finalResponse = null;

    while (!finalResponse) {
      const { done, value } = await reader.read();

      if (done) {
        break;
      }

      buffer += decoder.decode(value, { stream: true });

      try {
        const jsonStart = buffer.lastIndexOf("{");
        const jsonEnd = buffer.lastIndexOf("}");

        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
          const jsonStr = buffer.slice(jsonStart, jsonEnd + 1);
          finalResponse = JSON.parse(jsonStr);
        }
      } catch (parseError) {
        // Continue accumulating buffer if parsing fails
        continue;
      }
    }

    return finalResponse;
  } catch (error) {
    return false;
  }
}

export async function getNodeVersions(
  projectId,
  node_id,
  node_type,
  tenant_id
) {
  const url = `${backend_base_url}/discussion/${node_id}/versions/${node_type}?project_id=${projectId}&tenant_id=${tenant_id}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });
    if (!response.ok) {
      throw new Error("Failed to get the node versions");
    }
    const data = await response.json();
    return data;
  } catch (error) {}
}

export async function checkCodeMaintenanceLiveStatus(
  project_id,
  ipAddress,
  selectedRepos,
  sessionName,
  sessionDescription
) {
  const url = `${backend_base_url}/batch/check-live-status-code-maintenance/?project_id=${project_id}&ip_address=${ipAddress}`;
  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify({
        selectedrepos: selectedRepos.all_repositories
          ? { all_repositories: true }
          : {
              all_repositories: false,
              repositories: selectedRepos.repositories,
            },
        session_name: sessionName,
        description: sessionDescription, // Add description to request body
      }),
    });

    if (!response.ok) {
      throw new Error("Failed to check live status");
    }

    // Handle streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = "";
    let finalResponse = null;

    while (!finalResponse) {
      const { done, value } = await reader.read();

      if (done) {
        break;
      }

      buffer += decoder.decode(value, { stream: true });

      try {
        const jsonStart = buffer.lastIndexOf("{");
        const jsonEnd = buffer.lastIndexOf("}");

        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
          const jsonStr = buffer.slice(jsonStart, jsonEnd + 1);
          finalResponse = JSON.parse(jsonStr);
        }
      } catch (parseError) {
        // Continue accumulating buffer if parsing fails
        continue;
      }
    }

    return finalResponse;
  } catch (error) {
    return false;
  }
}

export async function getFlowlineStatus(projectId) {
  const base_url = backend_base_url;
  const url = `${base_url}/node/flowline/${projectId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to fetch flowline method");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getReconfigNodeStatus(project_id) {
  const base_url = backend_base_url;
  const url = `${base_url}/discussion/get-reconfig-status/${project_id}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to fetch the reconfig node status");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getReconfigNodeSectionFlag(project_id) {
  const base_url = backend_base_url;
  const url = `${base_url}/discussion/get-reconfig-section-flag-status/${project_id}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to fetch the reconfig node section flag status");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function list_all_documents_from_s3(task_id) {
  const base_url = backend_base_url;
  const url = `${base_url}/batch/list-document-from-s3?task_id=${task_id}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to list the all documents from s3 ");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function get_document_content_from_s3(task_id, title) {
  const base_url = backend_base_url;
  const url = `${base_url}/batch/retrive-document-content-from-s3?task_id=${task_id}&title=${title}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to retrive documents content from s3 ");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function checkSubscriptionStatus() {
  let base_url = backend_base_url;
  const url = `${base_url}/payment/check-subscription-status`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (response.ok) {
      return await response.json();
    } else {
      const errorData = await response.json();
      throw new Error(
        errorData?.detail || "Failed to check subscription status"
      );
    }
  } catch (error) {
    throw error;
  }
}

export async function getUserPlan() {
  const base_url = backend_base_url;
  const url = `${base_url}/users/myplan/`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to retrieve user plan");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}
export async function getB2cUserCostPlan() {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/b2c_user_cost_plan`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to retrieve b2c users plan');
    }

    return await response.json();
  } catch (error) {

    throw error;
  }
}

/**
 * Upgrades a user's plan in the B2C system
 * @param {string} userId - The ID of the user to upgrade
 * @param {string} plan - The plan to upgrade to (e.g., "premium_pro")
 * @param {string} tenantId - The tenant ID (should be "b2c" for B2C users)
 * @param {number} credits - The number of credits to allocate
 * @returns {Promise<Object>} - The response from the API
 */
export async function upgradeUserPlan(userId, plan, tenantId, credits) {
  if (!userId || !plan || !tenantId || !credits) {
    throw new Error('User ID, plan, tenant ID, and credits are required');
  }

  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/upgrade-user-plan`;

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({
        user_id: userId,
        plan: plan,
        tenant_id: tenantId,
        credits: credits
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData?.detail || `Failed to upgrade user plan: ${response.status}`);
    }

    return await response.json();
  } catch (error) {

    throw error;
  }
}

export async function addBulkUsers({ userData }) {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/bulk_add_users_to_organization`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      throw new Error("Failed to add bulk users");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}
export async function getAllAvailablePod() {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/get_all_available_pod`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to get all the available pods");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getAllUsedPod() {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/get_all_used_pod`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to get all the used pods");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function checkProjectIdToPod(project_id) {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/check_project_id_to_pod_map/${project_id}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to check project id to  pods");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function deleteProjectIdToPod(project_id) {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/delete_project_to_pod_map/${project_id}`;

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to delete project id to  pods");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function deletePodByPodId(pod_id) {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/delete_pod_by_pod_id/${pod_id}`;

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to delete pod by pod id");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function deleteAvailablePodByPodId(pod_id) {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/delete_available_pod/${pod_id}`;

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to delete available pod by pod id");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function deleteAllAvailablePod() {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/delete_all_available_pods`;

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to delete entire available pod ");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function deleteAllUsedPod() {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/delete_all_used_pods`;

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to delete entire used pod ");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function deleteMultipleUsedPods({ pod_ids }) {
  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/delete_multiple_used_pods`;

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: await getHeaders(),
      body: JSON.stringify({ pod_ids }),
    });

    if (!response.ok) {
      throw new Error("Failed to delete entire used pod ");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

// Supabase endpoints

export async function connectToSupabase(project_id) {
  const base_url = backend_base_url;
  const url = `${base_url}/supabase/connect-supabase/${project_id}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to connect to supabase ");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function listSupabaseOrg(project_id) {
  const base_url = backend_base_url;
  const url = `${base_url}/supabase/supabase/organizations?project_id=${project_id}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to list the supabase organizations ");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getSupabaseProjectDetails(
  supabase_project_id,
  project_id
) {
  const base_url = backend_base_url;
  const url = `${base_url}/supabase/supabase/projects/${supabase_project_id}?project_id=${project_id}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to get the supabase project details ");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function listSupabaseProjects(project_id, organization_id, include_all_status = true) {
  const base_url = backend_base_url;
  let url = `${base_url}/supabase/supabase/projects?project_id=${project_id}&include_all_status=${include_all_status}`;
  if (organization_id) {
    url += `&organization_id=${organization_id}`;
  }

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to list supabase projects");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function checkSupabaseProjectReadiness(project_id, project_id_supabase) {
  const base_url = backend_base_url;
  const url = `${base_url}/supabase/supabase/project-readiness/${project_id_supabase}?project_id=${project_id}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to check project readiness");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function bootstrapSupabaseProject(project_id, project_id_supabase, db_password = null) {
  const base_url = backend_base_url;
  let url = `${base_url}/supabase/supabase/bootstrap/${project_id_supabase}?project_id=${project_id}`;
  if (db_password) {
    url += `&db_password=${encodeURIComponent(db_password)}`;
  }

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to bootstrap supabase project");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

// Utility function to get status display information
export function getSupabaseProjectStatusDisplay(project) {
  const statusConfig = {
    success: {
      color: '#10B981', // green
      bgColor: '#D1FAE5',
      icon: '✅',
      textColor: '#065F46'
    },
    loading: {
      color: '#F59E0B', // amber
      bgColor: '#FEF3C7',
      icon: '⏳',
      textColor: '#92400E'
    },
    warning: {
      color: '#EF4444', // red
      bgColor: '#FEE2E2',
      icon: '⚠️',
      textColor: '#991B1B'
    },
    error: {
      color: '#EF4444', // red
      bgColor: '#FEE2E2',
      icon: '❌',
      textColor: '#991B1B'
    }
  };

  const config = statusConfig[project.ui_status] || statusConfig.warning;

  return {
    ...config,
    message: project.status_message,
    description: project.description,
    canContinue: project.can_continue,
    isReady: project.is_ready
  };
}

export async function disconnectSupabaseProject(project_id) {
  const base_url = backend_base_url;
  let url = `${base_url}/supabase/supabase/disconnect/${project_id}`;

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to disconnect supabase");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function createSupabaseProject(project_id, project_data) {
  const base_url = backend_base_url;
  const url = `${base_url}/supabase/supabase/projects?project_id=${project_id}`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify(project_data),
    });

    if (!response.ok) {
      // Extract error details from the response
      const errorData = await response.json().catch(() => ({}));
      console.log("🔍 API Error Response:", errorData);
      
      // Return error details in a structured format
      return {
        success: false,
        error: JSON.stringify(errorData),
        status: response.status
      };
    }

    return await response.json();
  } catch (error) {
    // If we can't parse the response, return a structured error
    return {
      success: false,
      error: error.message,
      status: 500
    };
  }
}
export async function updateSupabaseDB(
  project_id,
  project_id_supabase
) {
  const base_url = backend_base_url;
  const url = `${base_url}/supabase/supabase/update_db`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify({
        project_id,
        project_id_supabase,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || "Failed to update supabase database");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export const updateTermsAcceptance = async () => {
  try {
    const headers = await getHeaders();
    const response = await fetch(`${backend_base_url}/users/accept-terms`, {
      method: "POST",
      headers: headers,
    });

    if (!response.ok) {
      throw new Error("Failed to update terms acceptance");
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating terms acceptance:", error);
    throw error;
  }
};

// fetching the referred  users

export const getReferredUsers = async () => {
  let base_url = backend_base_url;
  const url = `${base_url}/manage/org/referred/users/`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Error in fetching referred users");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

export async function resumeStartCodeMaintenance(
  projectId,
  resumeTaskId,
  selectedRepos = null,
  sessionName = null,
  sessionDescription = null
) {
  let url = `${backend_base_url}/batch/start_code_maintenance/${projectId}/`;

  // Build query parameters
  const params = new URLSearchParams();
  params.append("resume", "true");
  params.append("resume_task_id", resumeTaskId);

  url += `?${params.toString()}`;

  // Prepare request body with maintenance-specific data
  const requestBody = {
    selectedrepos: selectedRepos || { all_repositories: true },
    session_name: sessionName || `Resume Session ${resumeTaskId}`,
    description:
      sessionDescription ||
      `Resumed maintenance session for task ${resumeTaskId}`,
    request_timestamp: new Date().toISOString(),
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify(requestBody),
    });

    if (response.ok) {
      // Check if response is SSE format
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("text/event-stream")) {
        // Handle SSE response - read the stream
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        let result = "";
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          result += decoder.decode(value, { stream: true });
        }

        // Parse all SSE messages and find the one with end: true and task_id
        const lines = result.split('\n');
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line.startsWith("data: ")) {
            try {
              const jsonData = line.substring(6); // Remove 'data: ' prefix
              const parsedData = JSON.parse(jsonData);
              
              // Look for the final message with end: true AND task_id
              if (parsedData.end === true && parsedData.task_id) {
                return parsedData;
              }
            } catch (error) {
              console.warn("Failed to parse SSE line:", line, error);
              continue;
            }
          }
        }

        throw new Error("No available pods found");
      } else {
        // Regular JSON response
        return await response.json();
      }
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.detail || "Failed to resume code maintenance");
    }
  } catch (error) {
    console.error("Resume code maintenance error:", error);
    throw error;
  }
}

// New function that mimics the exact curl command that works
export async function startCodeMaintenanceDirect(
  projectId,
  selectedRepos = null,
  sessionName = null,
  sessionDescription = null
) {
  const url = `${backend_base_url}/batch/start_code_maintenance/${projectId}/`;

  // Use exact same structure as working curl command
  const requestBody = {
    selectedrepos: selectedRepos || { all_repositories: true },
    session_name: sessionName || "Untitled",
    description: sessionDescription || "Resumed maintenance session",
    request_timestamp: new Date().toISOString(),
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify(requestBody),
    });

    if (response.ok) {
      // Check if response is SSE format
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("text/event-stream")) {
        // Handle SSE response - read the stream
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        let result = "";
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          result += decoder.decode(value, { stream: true });
        }

        // Parse the last SSE message which should contain the final result
        const lines = result.split("\n");
        for (let i = lines.length - 1; i >= 0; i--) {
          const line = lines[i].trim();
          if (line.startsWith("data: ")) {
            try {
              const jsonData = line.substring(6); // Remove 'data: ' prefix
              const parsedData = JSON.parse(jsonData);
              return parsedData;
            } catch (error) {
              console.warn("Failed to parse SSE line:", line, error);
              continue;
            }
          }
        }

        throw new Error("No valid JSON data found in SSE response");
      } else {
        // Regular JSON response
        return await response.json();
      }
    } else {
      const errorData = await response.json();
      throw new Error(errorData?.detail || "Failed to start code maintenance");
    }
  } catch (error) {
    console.error("Direct start code maintenance error:", error);
    throw error;
  }
}

export async function downloadDiscussionLogs(discussionId) {
  // Check if tenant is allowed to download logs
  const tenantId = Cookies.get("tenant_id");
  const allowedTenants = ["T0005", "T0015", "T0002", "rdk7542"];

  if (!allowedTenants.includes(tenantId)) {
    return {
      success: false,
      message: "Your tenant does not have permission to download logs",
    };
  }

  try {
    // Construct URL for the download endpoint
    const url = `${code_query_backend_base_url}/code_query/download-logs/${discussionId}`;

    // Use fetch with proper authentication headers
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Error: ${response.status} ${response.statusText}`);
    }

    // Get the file as a blob
    const blob = await response.blob();

    // Create a URL for the blob
    const downloadUrl = window.URL.createObjectURL(blob);

    // Create and trigger download link
    const a = document.createElement("a");
    a.style.display = "none";
    a.href = downloadUrl;
    a.download = `llm_knowledge_${discussionId}.log`;
    document.body.appendChild(a);
    a.click();

    // Clean up
    window.URL.revokeObjectURL(downloadUrl);
    document.body.removeChild(a);

    return {
      success: true,
      message: "Log download initiated",
    };
  } catch (error) {
    console.error("Error downloading logs:", error);
    return {
      success: false,
      message: "Failed to download logs: " + error.message,
    };
  }
}

export async function importRequirementsFromExcel(projectId, formData) {
  let base_url = backend_base_url;
  const url = `${base_url}/discussion/import_requirements/${projectId}`;
  const headers = getHeadersRaw();
  delete headers["Content-Type"];
  try {
    const response = await fetch(url, {
      method: "POST",
      headers: headers, // Ensure getHeaders does not set Content-Type, as FormData handles it
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Failed to import requirements: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function fetchTaskDocumentation(taskId) {
  let base_url = backend_base_url;
  let header = await getHeaders();

  const url = `${base_url}/doc_ingestion/task-documentation/${taskId}`;

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: header,
    });

    if (!response.ok) {
      throw new Error('Failed to fetch task documentation');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching task documentation:', error);
    throw error;
  }
}

export async function exportToDocx(projectId, nodeType) {
  let base_url = backend_base_url;
  const url = `${base_url}/node/${projectId}/docx?node_type=${nodeType}`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to export to DOCX: ${response.status}`);
    }
    
    const blob = await response.blob();
    // Create a download link and trigger it
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `${nodeType}_${projectId}_export.docx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
    
    return blob;
  } catch (error) {
    console.error('Export error:', error);
    throw error;
  }
}

// ----------------------------------------------------------------------------------------------------------------------------------------------//
// DEPLOYMENT DASHBOARD APIs
// ----------------------------------------------------------------------------------------------------------------------------------------------//

/**
 * Get Overall Deployment Statistics
 * GET /deployment-dashboard/stats
 */
export async function getDeploymentOverallStats(
  dateFrom = null,
  dateTo = null,
  status = null,
  tenantId = null
) {
  const base_url = backend_base_url;
  let url = `${base_url}/manage/super/deployment-dashboard/stats`;

  const params = new URLSearchParams();

  if (dateFrom) {
    params.append("date_from", dateFrom);
  }

  if (dateTo) {
    params.append("date_to", dateTo);
  }

  if (status && status !== "all") {
    params.append("status", status);
  }

  if (tenantId) {
    params.append("tenant_id", tenantId);
  }

  if (params.toString()) {
    url += "?" + params.toString();
  }

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch deployment stats: ${response.status}`);
    }

    const data = await response.json();
    return data.stats;
  } catch (error) {
    console.error("Error fetching deployment stats:", error);
    throw error;
  }
}

/**
 * Get Deployment Chart Data
 * GET /deployment-dashboard/chart-data
 */
export async function getDeploymentChartData(
  dateFrom = null,
  dateTo = null,
  status = null,
  granularity = "day",
  tenantId = null
) {
  const base_url = backend_base_url;
  let url = `${base_url}/manage/super/deployment-dashboard/chart-data`;

  const params = new URLSearchParams();

  if (dateFrom) {
    params.append("date_from", dateFrom);
  }

  if (dateTo) {
    params.append("date_to", dateTo);
  }

  if (status && status !== "all") {
    params.append("status", status);
  }

  if (tenantId) {
    params.append("tenant_id", tenantId);
  }

  params.append("granularity", granularity);

  if (params.toString()) {
    url += "?" + params.toString();
  }

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch deployment chart data: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching deployment chart data:", error);
    throw error;
  }
}

/**
 * Get All Tenants with Deployment Statistics
 * GET /deployment-dashboard/tenants
 */
export async function getDeploymentTenants(
  dateFrom = null,
  dateTo = null,
  status = null
) {
  const base_url = backend_base_url;
  let url = `${base_url}/manage/super/deployment-dashboard/tenants`;

  const params = new URLSearchParams();

  if (dateFrom) {
    params.append("date_from", dateFrom);
  }

  if (dateTo) {
    params.append("date_to", dateTo);
  }

  if (status && status !== "all") {
    params.append("status", status);
  }

  if (params.toString()) {
    url += "?" + params.toString();
  }

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch deployment tenants: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching deployment tenants:", error);
    throw error;
  }
}

/**
 * Get Projects in Tenant
 * GET /deployment-dashboard/{tenant_id}
 */
export async function getProjectsByTenant(
  tenantId,
  dateFrom = null,
  dateTo = null,
  status = null,
  page = 1,
  limit = 50
) {
  if (!tenantId) {
    throw new Error("Tenant ID is required");
  }

  const base_url = backend_base_url;
  let url = `${base_url}/manage/super/deployment-dashboard/${tenantId}`;

  const params = new URLSearchParams();

  if (dateFrom) {
    params.append("date_from", dateFrom);
  }

  if (dateTo) {
    params.append("date_to", dateTo);
  }

  if (status && status !== "all") {
    params.append("status", status);
  }

  params.append("page", page.toString());
  params.append("limit", limit.toString());

  if (params.toString()) {
    url += "?" + params.toString();
  }

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch projects: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching projects:", error);
    throw error;
  }
}

/**
 * Get Deployments in Project
 * GET /deployment-dashboard/{tenant_id}/{project_id}
 */
export async function getDeploymentsByProject(
  tenantId,
  projectId,
  dateFrom = null,
  dateTo = null,
  status = null,
  page = 1,
  limit = 20
) {
  if (!tenantId || !projectId) {
    throw new Error("Tenant ID and Project ID are required");
  }

  const base_url = backend_base_url;
  let url = `${base_url}/manage/super/deployment-dashboard/${tenantId}/${projectId}`;

  const params = new URLSearchParams();

  if (dateFrom) {
    params.append("date_from", dateFrom);
  }

  if (dateTo) {
    params.append("date_to", dateTo);
  }

  if (status && status !== "all") {
    params.append("status", status);
  }

  params.append("page", page.toString());
  params.append("limit", limit.toString());

  if (params.toString()) {
    url += "?" + params.toString();
  }

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch deployments: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching deployments:", error);
    throw error;
  }
}

/**
 * Get Specific Deployment Details
 * GET /deployment-dashboard/{tenant_id}/{project_id}/{deployment_id}
 */
export async function getDeploymentDetails(tenantId, projectId, deploymentId) {
  if (!tenantId || !projectId || !deploymentId) {
    throw new Error("Tenant ID, Project ID, and Deployment ID are required");
  }

  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/deployment-dashboard/${tenantId}/${projectId}/${deploymentId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch deployment details: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching deployment details:", error);
    throw error;
  }
}

/**
 * Get Recent Deployments
 * GET /deployment-dashboard/recent-deployments
 */
export async function getRecentDeployments(limit = 10, tenantId = null) {
  const base_url = backend_base_url;
  let url = `${base_url}/manage/super/deployment-dashboard/recent-deployments`;

  const params = new URLSearchParams();
  params.append("limit", limit.toString());

  if (tenantId) {
    params.append("tenant_id", tenantId);
  }

  if (params.toString()) {
    url += "?" + params.toString();
  }

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch recent deployments: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching recent deployments:", error);
    throw error;
  }
}

/**
 * Update Deployment Status (Admin Only)
 * POST /deployment-dashboard/{tenant_id}/{project_id}/{deployment_id}/update-status
 */
export async function updateDeploymentStatus(tenantId, projectId, deploymentId, newStatus) {
  if (!tenantId || !projectId || !deploymentId || !newStatus) {
    throw new Error("All parameters are required for status update");
  }

  const base_url = backend_base_url;
  let url = `${base_url}/manage/super/deployment-dashboard/${tenantId}/${projectId}/${deploymentId}/update-status`;

  const params = new URLSearchParams();
  params.append("new_status", newStatus);
  url += "?" + params.toString();

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to update deployment status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating deployment status:", error);
    throw error;
  }
}

/**
 * Delete Deployment (Admin Only)
 * DELETE /deployment-dashboard/{tenant_id}/{project_id}/{deployment_id}
 */
export async function deleteDeployment(tenantId, projectId, deploymentId) {
  if (!tenantId || !projectId || !deploymentId) {
    throw new Error("Tenant ID, Project ID, and Deployment ID are required");
  }

  const base_url = backend_base_url;
  const url = `${base_url}/manage/super/deployment-dashboard/${tenantId}/${projectId}/${deploymentId}`;

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to delete deployment: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error deleting deployment:", error);
    throw error;
  }
}

export async function getBranchesByUrl(repositoryUrl) {
  let base_url = backend_base_url;

  try {
    const response = await fetch(`${base_url}/oauth/github/get-branches-by-url`, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify({
        repository_url: repositoryUrl
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch branches: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getFigmaHtTML(project_id,tenant_id,fileName) {
  let base_url = backend_base_url;

  try {
    const response = await fetch(`${base_url}/file/assets/${tenant_id}/${project_id}/${fileName}`, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to get the figma html: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

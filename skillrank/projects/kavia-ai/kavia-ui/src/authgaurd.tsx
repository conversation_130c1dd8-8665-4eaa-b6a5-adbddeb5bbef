"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import Cookies from "js-cookie";

export default function AuthGuard({ children }: { children: React.ReactNode }) {
  const router = useRouter();

  useEffect(() => {
    const idToken = Cookies.get("idToken");
    const refreshToken = Cookies.get("refreshToken");
    
    if (!idToken && !refreshToken) {
      Object.keys(Cookies.get()).forEach((cookieName) => {
        Cookies.remove(cookieName);
      });

      const url = new URL(window.location.href);
      const urlTenantId = url.searchParams.get("tenant_id");
      const storedTenantId = localStorage.getItem("tenant_id");
      const tenantId =
        urlTenantId && urlTenantId !== "null"
          ? urlTenantId
          : storedTenantId || "T0000";

      if (tenantId && tenantId !== "T0000") {
        localStorage.setItem("tenant_id", tenantId);
      }

      const currentPath = window.location.pathname;
      const isLoginPage = currentPath.includes("login");
      const isSetPasswordPage = currentPath.includes("set_password");
      const isSignUpPage = currentPath.includes("sign_up");
      const isConfirmSignupPage = currentPath.includes("confirm_signup");
      const isForgotPasswordPage = currentPath.includes("forgot_password");
      const isConfirmForgotPasswordPage = currentPath.includes(
        "confirm_forgot_password"
      );
      const isGoogleCallbackPage = currentPath.includes("sso");
      const isBuildPage = currentPath == '/';
      const isPricingPage = currentPath == '/pricing';
      // If we're on any of the public auth pages, don't redirect
      if (
        isLoginPage ||
        isSetPasswordPage ||
        isSignUpPage ||
        isConfirmSignupPage ||
        isForgotPasswordPage ||
        isConfirmForgotPasswordPage ||
        isGoogleCallbackPage ||
        isBuildPage ||
        isPricingPage
      ) {
        return; 
      }
      router.replace(`/users/login`);
      return; 
    }
  }, [router]); 
  return <>{children}</>;
}

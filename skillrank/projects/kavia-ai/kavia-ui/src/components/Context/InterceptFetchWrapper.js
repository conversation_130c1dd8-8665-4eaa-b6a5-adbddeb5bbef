'use client';

import { usePlanRestriction } from './PlanRestrictionContext';
import PremiumOverlay from '../PremiumOverlay';
import { useEffect, useState } from 'react';
import Cookies from 'js-cookie';
import { refreshAccessToken } from '../../utils/api';
import { removeCookie } from '../../utils/auth';

const InterceptFetchWrapper = ({ children }) => {
    const { showPlanRestriction, setShowPlanRestriction, setCreditLimitCrossed } = usePlanRestriction();
    const [isUnauthorized, setIsUnauthorized] = useState(false);
    const [isCreditLimitExceeded, setIsCreditLimitExceeded] = useState(false);

    useEffect(() => {
        const originalFetch = window.fetch;

        window.fetch = async (url, options) => {
            try {
                const response = await originalFetch(url, options);

                // Handle authentication errors (401/403) with token refresh
                if ((response.status === 401 || response.status === 403) && !url.includes('/auth/refresh_token')) {
                    const refreshToken = Cookies.get('refreshToken');
                    const tenant_id = Cookies.get('tenant_id');
                    
                    if (refreshToken && tenant_id) {
                        try {
                            console.log('Token expired, attempting refresh via interceptor...');
                            await refreshAccessToken(refreshToken, tenant_id);
                            
                            // Retry the original request with new token
                            const retryResponse = await originalFetch(url, options);
                            console.log('Request retried successfully after token refresh');
                            return retryResponse;
                        } catch (refreshError) {
                            console.error('Token refresh failed in interceptor:', refreshError);
                            // Clean up invalid tokens
                            removeCookie("idToken");
                            removeCookie("refreshToken");
                            removeCookie("userId");
                            removeCookie("username");
                            removeCookie("email");
                            
                            // Return original response to let caller handle the authentication failure
                            return response;
                        }
                    }
                }

                if (response.status === 402) {
                    // Clone the response to read the body
                    const clonedResponse = response.clone();
                    try {
                        const data = await clonedResponse.json();
                        
                        if (data.detail === "You are not authorized to access this resource.") {
                            setIsUnauthorized(true);
                            setIsCreditLimitExceeded(false);
                        } else if (data.detail === "Free Credits Used Up.") {
                            setIsUnauthorized(false);
                            setIsCreditLimitExceeded(true);
                            setCreditLimitCrossed(true);
                        } else {
                            // Default case
                            setIsCreditLimitExceeded(true);
                            setCreditLimitCrossed(true);
                        }
                        
                        setShowPlanRestriction(true);
                    } catch (parseError) {
                        
                        // Default behavior for unparseable responses
                        setIsCreditLimitExceeded(true);
                        setCreditLimitCrossed(true);
                        setShowPlanRestriction(true);
                    }
                }

                return response;
            } catch (error) {
                

                if (url.includes('/auth/') || url.includes('/login')) {
                    return new Response(JSON.stringify({
                        error: true,
                        message: error.message || 'Network error occurred'
                    }), {
                        status: 500,
                        headers: { 'Content-Type': 'application/json' }
                    });
                }
                throw error;
            }
        };

        return () => {
            window.fetch = originalFetch;
        };
    }, [setShowPlanRestriction, setCreditLimitCrossed]);

    const handleClose = () => {
        setShowPlanRestriction(false);
        setIsUnauthorized(false);
        setIsCreditLimitExceeded(false);
    };

    return (
        <>
            {children}
            {showPlanRestriction && (
                <PremiumOverlay 
                    isCreditLimitExceeded={isCreditLimitExceeded} 
                    isUnauthorized={isUnauthorized} 
                    onClose={handleClose} 
                    allowClose={true} 
                />
            )}
        </>
    );
};

export default InterceptFetchWrapper;

import React from 'react'
import PropTypes from 'prop-types'

const SingleOptionElement = ({ option, messageId, onOptionSelect }) => {
  return (
    <div className="bg-gray-50 p-2 rounded text-sm font-normal mb-1">
      <button
        className="w-full mb-1 pb-1 border-b border-gray-200 last:border-b-0 last:mb-0 last:pb-0 text-left hover:bg-blue-50 active:bg-blue-100 transition-colors duration-150 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
        onClick={() => onOptionSelect(option, messageId)}
      >
        {option}
      </button>
    </div>
  )
}

SingleOptionElement.propTypes = {
    option: PropTypes.string.isRequired,
    messageId: PropTypes.string.isRequired,
    onOptionSelect: PropTypes.func.isRequired
}

export default SingleOptionElement
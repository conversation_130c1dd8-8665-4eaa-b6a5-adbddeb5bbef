.cga-option {
  cursor: pointer !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem 1rem !important;
  margin: 0.5rem 0.5rem 0.5rem 0 !important;
  border: 2px solid #e5e7eb !important;
  background-color: #ffffff !important;
  color: #374151 !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  line-height: 1.25rem !important;
  transition: all 0.2s ease-in-out !important;
  display: inline-block !important;
  max-width: calc(100% - 1rem) !important;
  min-width: fit-content !important;
  box-sizing: border-box !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
  user-select: none !important;
  -webkit-tap-highlight-color: transparent !important;
  vertical-align: top !important;
  text-decoration: none !important;
  outline: none !important;
}

.cga-option:hover {
  background-color: #fb923c !important;
  border-color: #fb923c !important;
  color: #ffffff !important;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-1px) !important;
}

.cga-option:active {
  background-color: #f3f4f6 !important;
  border-color: #6b7280 !important;
  transform: translateY(0) !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.cga-option:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  border-color: #3b82f6 !important;
}

/* Container for options */
.cga-options-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  max-width: 100%;
  overflow: hidden;
  margin-top: 1rem;
  padding: 0.5rem;
  border-top: 1px solid #e5e7eb;
}

/* Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.cga-option {
  animation: fadeInUp 0.3s ease-out !important;
}


/* Selected option styling */
.option-selected {
  background-color: #fb923c !important;
  border: 2px solid #fb923c !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  cursor: default !important;
  padding: 0.5rem 1rem !important;
  border-radius: 0.5rem !important;
  display: inline-block !important;
  margin: 0.25rem 0 !important;
}

/* Disabled option styling */
.option-disabled {
  background-color: #f3f4f6 !important;
  border: 2px solid #d1d5db !important;
  color: #6b7280 !important;
  cursor: not-allowed !important;
  opacity: 0.6 !important;
  padding: 0.5rem 1rem !important;
  border-radius: 0.5rem !important;
  display: inline-block !important;
  margin: 0.25rem 0 !important;
}

/* Ensure disabled options don't respond to hover */
.option-disabled:hover {
  background-color: #f3f4f6 !important;
  transform: none !important;
  box-shadow: none !important;
}
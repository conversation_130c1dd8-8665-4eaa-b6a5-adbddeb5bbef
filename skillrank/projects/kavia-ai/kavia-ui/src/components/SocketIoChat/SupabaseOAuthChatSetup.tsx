// src/components/SocketIoChat/SupabaseOAuthChatSetup.tsx

import React, { useState, useContext, useEffect } from "react";
import {
  X,
  Check,
  Loader2,
  Plus,
  Search,
  Eye,
  EyeOff,
  AlertTriangle,
  RefreshCcw,
  Copy,
  Hourglass,
} from "lucide-react";
import {
  connectToSupabase,
  listSupabaseOrg,
  listSupabaseProjects,
  createSupabaseProject,
  updateSupabaseDB,
  checkBootstrapStatus,
  getSupabaseProjectStatusDisplay,
  checkSupabaseProjectReadiness
} from "@/utils/api";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";

// Props for the OAuth setup component
interface SupabaseOAuthChatSetupProps {
  externalActionRequest: any;
  parentMessageId: string;
  onComplete: (result: any) => void;
  onClose: () => void;
  projectId: string; // Chat panel project ID
}

// Represents a Supabase project
interface SupabaseProject {
  id: string;
  name: string;
  region: string;
  dashboard_url: string;
  status?: string;
  is_ready?: boolean;
  can_continue?: boolean;
  status_message?: string;
  ui_status?: "success" | "loading" | "warning" | "error";
  description?: string;
}

// Defines the steps in the OAuth flow (added create_project step)
type OAuthStep =
  | "oauth_connect"
  | "select_project"
  | "create_project"
  | "creating_project"
  | "project_setup"
  | "completing";

// --- SVG Icons as React Components ---

// Main "Bolt" Icon for Supabase, used across multiple steps.
const SupabaseBoltIcon = () => (
  <svg width="48" height="48" viewBox="0 0 112 113" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="56" cy="56.5" r="56" fill="#EBFBEE" />
    <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint0_linear_supabase_bolt)" />
    <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint1_linear_supabase_bolt)" fillOpacity="0.2" />
    <path d="M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z" fill="#3ECF8E" />
    <defs>
      <linearGradient id="paint0_linear_supabase_bolt" x1="53.9738" y1="54.974" x2="94.1635" y2="71.8295" gradientUnits="userSpaceOnUse">
        <stop stopColor="#249361" />
        <stop offset="1" stopColor="#3ECF8E" />
      </linearGradient>
      <linearGradient id="paint1_linear_supabase_bolt" x1="36.1558" y1="30.578" x2="54.4844" y2="65.0806" gradientUnits="userSpaceOnUse">
        <stop stopColor="white" />
        <stop offset="1" stopColor="white" stopOpacity="0" />
      </linearGradient>
    </defs>
  </svg>
);

// Icon for the "No projects found" state
const NoProjectsIcon = () => (
  <div className="inline-block bg-slate-100 p-3 rounded-full mb-4">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 6C4 4.89543 4.89543 4 6 4H9.41421C9.93464 4 10.4346 4.21071 10.8284 4.58579L12.5858 6.34315C12.9796 6.7182 13.4796 6.92893 14 6.92893H18C19.1046 6.92893 20 7.82436 20 8.92893V18C20 19.1046 19.1046 20 18 20H6C4.89543 20 4 19.1046 4 18V6Z" fill="#CBD5E1" />
    </svg>
  </div>
);

// InfoBanner component for project creation errors
const InfoBanner = ({ errorMessage }: { errorMessage?: string }) => {
  const defaultMessage = "Project creation failed.";
  
  return (
    <div className="w-full flex items-center bg-yellow-50 p-4 border border-yellow-200 rounded-md shadow-sm mb-4">
      <div className="w-3 h-3 rounded-full bg-yellow-500 mr-3 animate-pulse"></div>
      <AlertTriangle className="text-yellow-600 mr-3 flex-shrink-0" size={20} />
      <span className="text-sm text-yellow-900 flex-1">
        {errorMessage || defaultMessage}
      </span>
    </div>
  );
};

// Main component
const SupabaseOAuthChatSetup: React.FC<SupabaseOAuthChatSetupProps> = ({
  onComplete,
  onClose,
  projectId,
}) => {
  const [currentStep, setCurrentStep] = useState<OAuthStep>("oauth_connect");
  const [projects, setProjects] = useState<SupabaseProject[]>([]);
  const [selectedProject, setSelectedProject] =
    useState<SupabaseProject | null>(null);
  const [dbPassword, setDbPassword] = useState("");
  const [dbPasswordError, setDbPasswordError] = useState("");
  const [showDbPassword, setShowDbPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Project creation state
  const [isCreatingProject, setIsCreatingProject] = useState(false);
  const [newProject, setNewProject] = useState({
    name: "my-awesome-app",
    organization_id: "",
    region: "us-east-1", // Default to US East (N. Virginia)
    db_password: "",
  });
  const [organizations, setOrganizations] = useState([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [checkingReadiness, setCheckingReadiness] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [copiedPassword, setCopiedPassword] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [bannerErrorMessage, setBannerErrorMessage] = useState<string>("");
  const [formErrors, setFormErrors] = useState<{ name?: string; password?: string }>({});
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [projectSetupStatus, setProjectSetupStatus] = useState<string>("");
  const [projectSetupProgress, setProjectSetupProgress] = useState<number>(0);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  const { showAlert } = useContext(AlertContext);

  // Test function to debug error parsing (you can call this in console)
  const testErrorParsing = (errorStr: string) => {
    console.log("🧪 Testing error parsing with:", errorStr);
    
    let errorMessage = "Failed to create project";
    let shouldShowBanner = false;
    
    if (errorStr.includes('"detail":')) {
      try {
        console.log("🔍 Attempting to parse detail field...");
        const errorObj = JSON.parse(errorStr);
        console.log("🔍 Parsed JSON:", errorObj);
        
        if (errorObj.detail) {
          // The detail field contains the actual error message
          errorMessage = errorObj.detail;
          console.log("🧪 Detail message extracted:", errorMessage);
          
          if (errorMessage.includes("already exists")) {
            errorMessage = "Project name already exists. Please choose a different name.";
            shouldShowBanner = false;
          }
        }
      } catch (parseError) {
        console.error("🔍 Error parsing detail:", parseError);
      }
    }
    
    console.log("🧪 Final result:", { errorMessage, shouldShowBanner });
    return { errorMessage, shouldShowBanner };
  };

  // Test function for result.error parsing (backend 400 responses)
  const testResultErrorParsing = (resultError: any) => {
    console.log("🧪 Testing result.error parsing with:", resultError);
    
    let errorMessage = "Failed to create project";
    
    if (resultError) {
      console.log("🔍 Result error field:", resultError);
      
      if (typeof resultError === 'string') {
        try {
          if (resultError.includes('"detail":')) {
            const errorObj = JSON.parse(resultError);
            if (errorObj.detail) {
              // The detail field contains the actual error message
              errorMessage = errorObj.detail;
              console.log("🔍 Detail message extracted from result:", errorMessage);
              
              if (errorMessage.includes("already exists")) {
                errorMessage = "Project name already exists. Please choose a different name.";
              }
            }
          } else if (resultError.includes('"message":')) {
            const messageMatch = resultError.match(/"message":"([^"]+)"/);
            if (messageMatch) {
              const message = messageMatch[1].replace(/\\"/g, '"');
              console.log("🔍 Message extracted from result:", message);
              
              if (message.includes("already exists")) {
                errorMessage = "Project name already exists. Please choose a different name.";
              } else {
                errorMessage = message;
              }
            }
          } else {
            errorMessage = resultError;
          }
        } catch (parseError) {
          console.error("🔍 Error parsing result.error:", parseError);
          errorMessage = resultError;
        }
      } else {
        errorMessage = JSON.stringify(resultError);
      }
    }
    
    console.log("🧪 Final result:", { errorMessage });
    return { errorMessage };
  };

  // Make test functions available globally for debugging
  useEffect(() => {
    (window as any).testSupabaseErrorParsing = testErrorParsing;
    (window as any).testResultErrorParsing = testResultErrorParsing;
    console.log("🧪 Test functions available at:");
    console.log("🧪 - window.testSupabaseErrorParsing");
    console.log("🧪 - window.testResultErrorParsing");
  }, []);

  // OAuth connection handler
  const handleOAuthConnect = async () => {
    setIsLoading(true);
    setConnectionError(null);
    
    try {
      const response = await connectToSupabase(projectId);

      // Check if Supabase is already connected
      if (response && response.success && response.status === "already_connected") {
        setIsConnected(true);
        showAlert("Supabase is already connected!", "success");
        setCurrentStep("select_project");
        setIsLoading(true);
        await fetchUserProjects();
        setIsLoading(false);
        return;
      }

      // Handle new OAuth connection
      if (response && response.auth_url) {
        const popup = window.open(
          response.auth_url,
          "supabase_oauth",
          "width=500,height=600,scrollbars=yes,resizable=yes"
        );

        if (!popup) {
          setConnectionError("Failed to open OAuth popup. Please allow popups for this site.");
          showAlert("Failed to open OAuth popup. Please allow popups for this site.", "error");
          setIsLoading(false);
          return;
        }

        let isCompleted = false;

        const checkPopupClosed = setInterval(async () => {
          if (popup?.closed && !isCompleted) {
            isCompleted = true;
            clearInterval(checkPopupClosed);
            clearTimeout(timeoutId);
            
            try {
              // Verify connection was successful by fetching projects
              await fetchUserProjects();
              setIsConnected(true);
              setCurrentStep("select_project");
              setIsLoading(false);
            } catch (fetchError) {
              console.error("Failed to fetch projects:", fetchError);
              setConnectionError("OAuth connection failed. Please try again.");
              showAlert("Failed to fetch Supabase projects", "error");
              setIsLoading(false);
              // Stay on oauth_connect step if connection fails
              setCurrentStep("oauth_connect");
            }
          }
        }, 1000);

        // Timeout handler
        const timeoutId = setTimeout(() => {
          if (!popup?.closed && !isCompleted) {
            isCompleted = true;
            clearInterval(checkPopupClosed);
            popup.close();
            setConnectionError("OAuth connection timed out. Please try again.");
            showAlert("OAuth timeout - please try again", "warning");
            setIsLoading(false);
            // Stay on oauth_connect step if connection fails
            setCurrentStep("oauth_connect");
          }
        }, 120000); // 2 minutes timeout

      } else {
        setConnectionError("Failed to get OAuth URL. Please try again.");
        showAlert("Failed to get OAuth URL", "error");
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Error connecting to Supabase:", error);
      setConnectionError("Failed to initiate Supabase connection. Please try again.");
      showAlert("Failed to initiate Supabase connection", "error");
      setIsLoading(false);
    }
  };

  // Fetch user's Supabase projects and organizations
  const fetchUserProjects = async () => {
    try {
      setIsInitialLoading(true);
      
      // Get organizations
      const organizationsResponse = await listSupabaseOrg(projectId);

      if (!organizationsResponse?.data?.length) {
        setProjects([]);
        setOrganizations([]);
        setIsInitialLoading(false);
        return;
      }

      // Store organizations for project creation
      setOrganizations(organizationsResponse.data);

      // Set default organization for new project
      if (organizationsResponse.data.length > 0) {
        setNewProject((prev) => ({
          ...prev,
          organization_id: organizationsResponse.data[0].id,
        }));
      }

      // Get projects for the first organization
      const organizationId = organizationsResponse.data[0].id;
      const projectsResponse = await listSupabaseProjects(
        projectId,
        organizationId,
        true
      );

      if (projectsResponse?.success && projectsResponse?.data) {
        // Sort projects: newly created first, then by name
        const sortedProjects = projectsResponse.data.sort(
          (a: SupabaseProject, b: SupabaseProject) => {
            // Prioritize ACTIVE_HEALTHY projects
            if (a.status === "ACTIVE_HEALTHY" && b.status !== "ACTIVE_HEALTHY")
              return -1;
            if (b.status === "ACTIVE_HEALTHY" && a.status !== "ACTIVE_HEALTHY")
              return 1;

            // Then sort by name
            return a.name.localeCompare(b.name);
          }
        );

        setProjects(sortedProjects);
      } else {
        setProjects([]);
      }
    } catch (error) {
      console.error("Error fetching projects:", error);
      setProjects([]);
      setOrganizations([]);
    } finally {
      setIsInitialLoading(false);
    }
  };

  // Handle project selection
  const handleProjectSelect = async (project: SupabaseProject) => {
    setSelectedProject(project);

    if (!project.can_continue) {
      // Project is not ready, start checking readiness
      setCheckingReadiness(project.id);
      await checkProjectReadiness(project);
    }
    // Note: Removed the automatic step change to configure_database
  };

  // Check if project is ready
  const checkProjectReadiness = async (project: SupabaseProject) => {
    try {
      const response = await checkSupabaseProjectReadiness(projectId, project.id);

      if (response.readiness?.can_continue) {
        // Update the project in the list
        setProjects(prev => prev.map(p =>
          p.id === project.id
            ? {
                ...p,
                ...response.readiness,
                is_ready: response.readiness.can_continue,
                can_continue: response.readiness.can_continue
              }
            : p
        ));

        // Update selected project
        setSelectedProject(prev => prev ? {
          ...prev,
          ...response.readiness,
          is_ready: response.readiness.can_continue,
          can_continue: response.readiness.can_continue
        } : null);

        setCheckingReadiness(null);
        // Note: Removed automatic navigation to configure_database
      } else {
        // Still not ready, check again in 10 seconds
        setTimeout(() => checkProjectReadiness(project), 10000);
      }
    } catch (error) {
      console.error('Failed to check readiness:', error);
      setCheckingReadiness(null);
    }
  };

  // Generate a strong password
  const generatePassword = () => {
    const length = 16;
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    setNewProject({ ...newProject, db_password: password });
    setShowPassword(true);
    if (formErrors.password) setFormErrors({ ...formErrors, password: undefined });
  };

  // Validate form fields
  const validateForm = (): boolean => {
    const errors: { name?: string; password?: string } = {};

    // Project name validation
    if (!newProject.name.trim()) {
      errors.name = 'Project name is required';
    } else if (newProject.name.length < 3) {
      errors.name = 'Project name must be at least 3 characters';
    } else if (!/^[a-zA-Z0-9-]+$/.test(newProject.name)) {
      errors.name = 'Project name can only contain letters, numbers, and hyphens';
    }

    // Password validation
    if (!newProject.db_password) {
      errors.password = 'Password is required';
    } else if (newProject.db_password.length < 8) {
      errors.password = 'Password must be at least 8 characters';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])/.test(newProject.db_password)) {
      errors.password = 'Password must contain uppercase, lowercase, and numbers';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Copy password to clipboard
  const copyPassword = async () => {
    if (newProject.db_password) {
      try {
        await navigator.clipboard.writeText(newProject.db_password);
        setCopiedPassword(true);
        setTimeout(() => setCopiedPassword(false), 2000);
      } catch (err) {
        console.error('Failed to copy password:', err);
      }
    }
  };

  // Add this useEffect for real-time status updates
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (selectedProject && !selectedProject.is_ready) {
      intervalId = setInterval(async () => {
        const isReady = await checkBootstrapStatus(
          projectId,
          selectedProject.id
        );

        if (isReady) {
          setSelectedProject((prev) =>
            prev
              ? {
                  ...prev,
                  is_ready: true,
                  can_continue: true,
                  status_message: "Ready to use",
                  ui_status: "success",
                  description:
                    "Project is fully initialized and ready for database operations",
                }
              : null
          );

          setProjects((prev) =>
            prev.map((p) =>
              p.id === selectedProject.id
                ? {
                    ...p,
                    is_ready: true,
                    can_continue: true,
                    status_message: "Ready to use",
                    ui_status: "success",
                  }
                : p
            )
          );

          showAlert("Project is now ready to use!", "success");
        }
      }, 5000); // Check every 5 seconds
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [selectedProject?.id, selectedProject?.is_ready]);
  

  const handleCreateProject = async () => {
    // Clear any banner errors before validation
    if (isVisible) {
      setIsVisible(false);
      setBannerErrorMessage("");
    }
    
    if (!validateForm()) {
        return;
    }

    setIsCreatingProject(true);
    setProjectSetupStatus("Creating your Supabase project...");
    setProjectSetupProgress(20);
    
    try {
        const organizationsResponse = await listSupabaseOrg(projectId);

        if (!organizationsResponse?.data?.length) {
            throw new Error("Unable to fetch organizations");
        }

        setProjectSetupProgress(40);
        setProjectSetupStatus("Setting up project infrastructure...");

        const projectData = {
            name: newProject.name,
            organization_id: organizationsResponse.data[0].id,
            region: newProject.region,
            db_password: newProject.db_password || undefined,
        };

        // Use standard project creation (bootstrap will happen in update_db)
        const result = await createSupabaseProject(projectId, projectData);

        if (result?.success) {
            setProjectSetupProgress(60);
            setProjectSetupStatus("Project created! Setting up database...");

            const createdProject: SupabaseProject = {
                id: result.data.project_id,
                name: newProject.name,
                region: newProject.region,
                dashboard_url: result.data.dashboard_url,
                status: "ACTIVE_HEALTHY",
                is_ready: false, // Will be updated after database config
                can_continue: true, // Allow immediate setup
                status_message: "Created successfully",
                ui_status: "success",
                description: "Project created successfully. Ready for setup."
            };

            setSelectedProject(createdProject);
            setDbPassword(newProject.db_password || result.data.db_password || "");
            
            // Move to project setup step to show progress
            setCurrentStep("project_setup");
            setProjectSetupProgress(80);
            setProjectSetupStatus("Finalizing database setup...");
            
            // Wait a bit to show the setup progress
            setTimeout(() => {
                setProjectSetupProgress(100);
                setProjectSetupStatus("Project setup complete!");
                
                setTimeout(() => {
                    showAlert(`Project "${newProject.name}" created successfully!`, "success");
                    // Navigate back to project selection with the new project
                    setCurrentStep("select_project");
                    fetchUserProjects();
                }, 1000);
            }, 2000);
            
        } else {
            // Handle 400 error responses from backend
            console.log("🔍 Result:", result);
            
            let errorMessage = "Failed to create project";
            
            // Check if result has the error structure we expect
            if (result?.error) {
                console.log("🔍 Result error field:", result.error);
                
                // Try to parse the error if it's a string
                if (typeof result.error === 'string') {
                    try {
                        // Check if it's JSON
                        if (result.error.includes('"detail":')) {
                            const errorObj = JSON.parse(result.error);
                            if (errorObj.detail) {
                                // The detail field contains the actual error message
                                errorMessage = errorObj.detail;
                                console.log("🔍 Detail message extracted:", errorMessage);
                                
                                // Check if it's a project name conflict
                                if (errorMessage.includes("already exists")) {
                                    errorMessage = "Project name already exists. Please choose a different name.";
                                }
                            }
                        } else if (result.error.includes('"message":')) {
                            // Direct message pattern
                            const messageMatch = result.error.match(/"message":"([^"]+)"/);
                            if (messageMatch) {
                                const message = messageMatch[1].replace(/\\"/g, '"');
                                console.log("🔍 Message extracted from result:", message);
                                
                                if (message.includes("already exists")) {
                                    errorMessage = "Project name already exists. Please choose a different name.";
                                } else {
                                    errorMessage = message;
                                }
                            }
                        } else {
                            // Use the error string directly
                            errorMessage = result.error;
                        }
                    } catch (parseError) {
                        console.error("🔍 Error parsing result.error:", parseError);
                        errorMessage = result.error;
                    }
                } else {
                    // If error is not a string, try to stringify it
                    errorMessage = JSON.stringify(result.error);
                }
            }
            
            // For project name conflicts, always show as banner to avoid form validation issues
            let shouldShowBanner = true;
            
            console.log("🔍 Final error message:", errorMessage);
            console.log("🔍 Should show banner:", shouldShowBanner);
            
            // Show error as banner
            setIsVisible(true);
            setBannerErrorMessage(errorMessage);
            setFormErrors({}); // Clear form errors when showing banner
            
            setCurrentStep("create_project"); // Go back to create form on error
        }
    } catch (error) {
        console.error("Error creating project:", error);
        
        // Handle any unexpected errors (network issues, etc.)
        let errorMessage = "Failed to create project due to an unexpected error";
        
        if (error instanceof Error) {
            errorMessage = error.message;
        }
        
        // Show error in form
        setFormErrors({ name: errorMessage });
        setCurrentStep("create_project");
    } finally {
        setIsCreatingProject(false);
    }
};

  // Handle database configuration completion (modified to work without database password requirement)
  const handleDatabaseComplete = async () => {
    if (!selectedProject?.id) {
      showAlert("No project selected", "error");
      return;
    }

    setCurrentStep("completing");
    setIsLoading(true);

    try {
      // Bootstrap the database connection
      const bootstrapResult = await updateSupabaseDB(
        projectId,
        selectedProject.id,
      );

      if (bootstrapResult?.success) {
        // Prepare the completion result - use default password if not provided
        const finalDbPassword = dbPassword || "defaultPassword";
        
        console.log("bootstrapResult", bootstrapResult);
        const result = {
          selectedProject,
          apiUrl: `https://${selectedProject.id}.supabase.co`,
          databaseUrl: `postgresql://postgres:${finalDbPassword}@db.${selectedProject.id}.supabase.co:5432/postgres`,
          anonKey: bootstrapResult.data?.anon_key || "PLACEHOLDER_ANON_KEY",
          serviceRoleKey:
            bootstrapResult.data?.service_role_key || "PLACEHOLDER_SERVICE_KEY",
        };

        onComplete(result);
      } else {
        throw new Error(
          bootstrapResult?.error || "Failed to bootstrap database"
        );
      }
    } catch (error) {
      console.error("Error completing database setup:", error);
      showAlert("Failed to complete database setup", "error");
      setCurrentStep("select_project");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle refresh projects
  const handleRefreshProjects = async () => {
    setIsRefreshing(true);
    try {
      await fetchUserProjects();
      showAlert("Projects refreshed successfully", "success");
    } catch (error) {
      console.error("Error refreshing projects:", error);
      showAlert("Failed to refresh projects", "error");
    } finally {
      setIsRefreshing(false);
    }
  };

  // Filter projects based on search query
  const filteredProjects = projects.filter((project) =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // --- Dynamic Header and Content Rendering ---

  const getTitle = () => {
    switch (currentStep) {
      case "oauth_connect":
        return "Connect to Supabase";
      case "select_project":
        return "Select Your Supabase Database";
      case "create_project":
        return "Create Supabase Project";
      case "completing":
        return "Completing Setup";
      default:
        return "Connect to Supabase";
    }
  };

  // Handle breadcrumb navigation
  const handleBreadcrumbClick = (stepIndex: number) => {
    const steps = getBreadcrumbSteps();
    const targetStep = steps[stepIndex];
    
    // Only allow navigation if connected
    if (!isConnected && targetStep !== 'Configure App') {
      showAlert("Please complete Supabase connection first", "warning");
      return;
    }
    
    switch (targetStep) {
      case 'Configure App':
        setCurrentStep("oauth_connect");
        break;
      case 'Select Database':
        setCurrentStep("select_project");
        break;
      case 'Create Project':
        setCurrentStep("create_project");
        break;
      default:
        break;
    }
  };

  const getBreadcrumbSteps = () => {
    let steps = ['Configure App'];
    if (currentStep === "select_project") {
      steps.push('Select Database');
    } else if (currentStep === "create_project") {
      steps.push('Select Database', 'Create Project');
    } else {
      steps.push('Connect Supabase');
    }
    return steps;
  };

  const getBreadcrumb = () => {
    const steps = getBreadcrumbSteps();

    return (
      <p className="text-xs text-slate-500 mt-1">
        {steps.map((step, index) => (
          <React.Fragment key={index}>
            <span 
              className={index === steps.length - 1 ? "text-slate-700 font-medium" : "text-primary hover:underline cursor-pointer"}
              onClick={index === steps.length - 1 ? undefined : () => handleBreadcrumbClick(index)}
            >
              {step}
            </span>
            {index < steps.length - 1 && <span className="mx-1">›</span>}
          </React.Fragment>
        ))}
      </p>
    );
  };

  const renderContent = () => {
    // Guard: Only allow project selection and creation steps if connected
    if (!isConnected && (currentStep === "select_project" || currentStep === "create_project" || currentStep === "creating_project" || currentStep === "project_setup")) {
      return (
        <div className="flex flex-col items-center text-center px-4 sm:px-8">
          <AlertTriangle className="w-16 h-16 text-red-500 mb-4" />
          <h2 className="text-xl font-semibold text-slate-800 mt-4 mb-2">Connection Required</h2>
          <p className="text-sm text-slate-500 max-w-md mb-6">
            You must complete the Supabase connection before accessing this step.
          </p>
          <button
            onClick={() => setCurrentStep("oauth_connect")}
            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm font-medium"
          >
            Connect to Supabase
          </button>
        </div>
      );
    }

    switch (currentStep) {
      case "oauth_connect":
        return (
          <div className="flex flex-col items-center text-center px-4 sm:px-8">
            <SupabaseBoltIcon />
            <h2 className="text-xl font-semibold text-slate-800 mt-4 mb-2">Connect to Supabase</h2>
            <p className="text-sm text-slate-500 max-w-md mb-8">
              Kavia needs access to your Supabase organization to set up your database and authentication.
            </p>
            
            {connectionError && (
              <div className="w-full max-w-md mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center">
                  <AlertTriangle className="text-red-600 mr-3 flex-shrink-0" size={20} />
                  <span className="text-sm text-red-800">{connectionError}</span>
                </div>
              </div>
            )}
          </div>
        );
      case "select_project":
        return (
          <div className="flex flex-col items-center text-center px-4 sm:px-8">
            <SupabaseBoltIcon />
            <h2 className="text-xl font-semibold text-slate-800 mt-4 mb-2">Select your database</h2>
            <p className="text-sm text-slate-500 max-w-lg mb-8">
              Connect with a Supabase project to manage your data, set up authentication, create backend functions, and more.
            </p>
            <div className="w-full text-left mb-2">
              <label className="text-sm font-medium text-slate-700">Select a project</label>
            </div>
            <div className="w-full">
              <div className='flex items-center justify-between gap-2'>
                <div className="relative mb-4 w-full ">
                  <Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                  <input
                    type="text"
                    placeholder="Search for a project"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    disabled={isInitialLoading}
                    className="w-full p-2 pl-10 border border-slate-300 rounded-md focus:ring-2 focus:ring-green-500/50 focus:border-green-500 outline-none disabled:bg-slate-50 disabled:cursor-not-allowed"
                  />
                </div>
                <button
                  onClick={handleRefreshProjects}
                  disabled={isRefreshing || isInitialLoading}
                  className="flex items-center justify-center -mt-4 px-3 py-2 border border-slate-300 rounded-md text-sm text-slate-600 hover:bg-slate-100 transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Refresh projects"
                >
                  <RefreshCcw className={`mr-1.5 w-3.5 h-3.5 ${isRefreshing ? "animate-spin" : ""}`}/>
                  {isRefreshing ? "Refreshing..." : "Refresh"}
                </button>
              </div>

              {isInitialLoading ? (
                <div className="w-full text-center border-2 border-dashed border-slate-200 rounded-lg py-10 px-6">
                  <Loader2 className="w-8 h-8 text-slate-400 animate-spin mx-auto mb-4" />
                  <p className="font-semibold text-slate-700">Loading projects...</p>
                  <p className="text-sm text-slate-500 mt-1">Please wait while we fetch your Supabase projects.</p>
                </div>
              ) : filteredProjects.length === 0 ? (
                <div className="w-full text-center border-2 border-dashed border-slate-200 rounded-lg py-10 px-6">
                  <NoProjectsIcon />
                  <p className="font-semibold text-slate-700">No projects found</p>
                  <p className="text-sm text-slate-500 mt-1">{searchQuery ? 'Try a different search term.' : 'You can create one below.'}</p>
                </div>
              ) : (
                <div className="w-full border border-slate-200 rounded-lg text-left max-h-48 overflow-y-auto">
                  {filteredProjects.map(project => {
                    const statusDisplay = getSupabaseProjectStatusDisplay(project);
                    const isSelected = selectedProject?.id === project.id;
                    const isCheckingThis = checkingReadiness === project.id;

                    return (
                      <label key={project.id} className={`flex items-center p-4 border-b border-slate-200 last:border-b-0 cursor-pointer hover:bg-slate-50 ${isSelected ? 'bg-green-50 border-green-300 ring-1 ring-green-300' : ''}`}>
                        <input
                          type="radio"
                          name="project"
                          checked={isSelected}
                          onChange={() => handleProjectSelect(project)}
                          className="h-4 w-4 border-slate-300 text-green-600 focus:ring-green-500"
                        />
                        <div className="ml-3 flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-slate-700">{project.name}</span>
                            <div className="flex items-center gap-2">
                              {isCheckingThis && (
                                <Loader2 className="w-4 h-4 animate-spin text-amber-500" />
                              )}
                              <div
                                className="px-2 py-1 rounded text-xs font-medium flex items-center gap-1"
                                style={{
                                  backgroundColor: statusDisplay.bgColor,
                                  color: statusDisplay.textColor
                                }}
                              >
                                <span>{statusDisplay.icon}</span>
                                <span>{statusDisplay.message}</span>
                              </div>
                            </div>
                          </div>
                          {!statusDisplay.canContinue && (
                            <div className="text-xs text-slate-500 mt-1">
                              {statusDisplay.description}
                            </div>
                          )}
                        </div>
                      </label>
                    );
                  })}
                </div>
              )}
              <div className="flex items-center my-6 w-full">
                <div className="flex-grow border-t border-slate-200"></div>
                <span className="flex-shrink mx-4 text-slate-400 text-xs font-medium">OR</span>
                <div className="flex-grow border-t border-slate-200"></div>
              </div>
              <button 
                onClick={() => setCurrentStep("create_project")} 
                disabled={isInitialLoading}
                className="w-full max-w-xs px-4 py-2 mx-auto bg-slate-800 text-white rounded-md hover:bg-slate-900 text-sm font-medium flex items-center justify-center disabled:bg-slate-400 disabled:cursor-not-allowed"
              >
                <Plus size={16} className="mr-2" />
                {isInitialLoading ? "Loading..." : "Create a new project"}
              </button>
            </div>
          </div>
        );
      case "create_project":
        return (
          <div className="flex flex-col items-center text-center px-4 sm:px-8">
            <SupabaseBoltIcon />
            <h2 className="text-xl font-semibold text-slate-800 mt-4 mb-2">Create your database</h2>
            <p className="text-sm text-slate-500 max-w-lg mb-8">
              Configure your database information.
            </p>
            
            <div className="w-full text-left space-y-4">
              {isVisible && <InfoBanner errorMessage={bannerErrorMessage} />}
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Project name
                </label>
                <input
                  type="text"
                  value={newProject.name}
                  onChange={(e) => {
                    setNewProject((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }));
                    // Clear any existing errors when user starts typing
                    if (formErrors.name) setFormErrors({ ...formErrors, name: undefined });
                    if (isVisible) {
                      setIsVisible(false);
                      setBannerErrorMessage("");
                    }
                  }}
                  placeholder="my-awesome-app"
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Database password
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    value={newProject.db_password}
                    onChange={(e) => {
                      setNewProject((prev) => ({
                        ...prev,
                        db_password: e.target.value,
                      }));
                      // Clear any existing errors when user starts typing
                      if (formErrors.password) setFormErrors({ ...formErrors, password: undefined });
                      if (isVisible) {
                        setIsVisible(false);
                        setBannerErrorMessage("");
                      }
                    }}
                    placeholder="Password"
                    className="w-full px-3 py-2 pr-12 border border-slate-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-400 hover:text-slate-600"
                  >
                    {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
                <p className="text-slate-500 text-sm mt-1">
                  This is the password of your Postgres database, so it must be strong and hard to guess.
                </p>
                <button
                  onClick={generatePassword}
                  className="text-green-600 hover:text-green-700 text-sm underline mt-1"
                >
                  Generate a password
                </button>
              </div>
            </div>
          </div>
        );
      case "creating_project":
        return (
          <div className="flex flex-col items-center justify-center h-full py-28 text-center">
            <Loader2 className="w-12 h-12 text-green-500 animate-spin mb-6" />
            <h2 className="text-xl font-semibold text-slate-800 mb-4">Creating Project</h2>
            <p className="text-slate-500 text-sm mb-6">
              Please wait while we create your Supabase project...
            </p>
            <div className="w-full max-w-md">
              <div className="bg-slate-100 rounded-full h-2 mb-2">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${projectSetupProgress}%` }}
                ></div>
              </div>
              <p className="text-xs text-slate-500">{projectSetupStatus}</p>
            </div>
          </div>
        );
      case "project_setup":
        return (
          <div className="flex flex-col items-center justify-center h-full py-28 text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
              <Check className="w-8 h-8 text-green-600" />
            </div>
            <h2 className="text-xl font-semibold text-slate-800 mb-4">Setting Up Project</h2>
            <p className="text-slate-500 text-sm mb-6">
              Your project has been created! Now setting up the database...
            </p>
            <div className="w-full max-w-md">
              <div className="bg-slate-100 rounded-full h-2 mb-2">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${projectSetupProgress}%` }}
                ></div>
              </div>
              <p className="text-xs text-slate-500">{projectSetupStatus}</p>
            </div>
          </div>
        );
      case "completing":
        return (
          <div className="flex flex-col items-center justify-center h-full py-28 text-center">
            <Hourglass className="w-10 h-10 text-slate-400 mb-4 [animation:spin_3s_linear_inclusive]" />
            <p className="text-slate-500 text-sm">
              Creating and setting up your Supabase project...
            </p>
          </div>
        );
      default: return null;
    }
  };

  const renderFooter = () => {
    // Guard: Only show project-related footers if connected
    if (!isConnected && (currentStep === "select_project" || currentStep === "create_project" || currentStep === "creating_project" || currentStep === "project_setup")) {
      return (
        <div className="w-full flex justify-center items-center">
          <button
            onClick={() => setCurrentStep("oauth_connect")}
            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm font-medium"
          >
            Connect to Supabase First
          </button>
        </div>
      );
    }

    switch (currentStep) {
      case "oauth_connect":
        return (
          <div className="w-full flex justify-end items-center">
            <button onClick={onClose} className="px-4 py-2 bg-slate-100 text-slate-700 rounded-md hover:bg-slate-200 text-sm font-medium">Back</button>
            <button
              onClick={handleOAuthConnect}
              disabled={isLoading}
              className="ml-3 px-4 py-2 rounded-md text-sm font-medium flex items-center justify-center transition-all duration-200 bg-green-500 text-white hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Connecting...
                </>
              ) : (
                'Continue to Supabase'
              )}
            </button>
          </div>
        );
      case "select_project":
        return (
          <div className="w-full flex justify-between items-center">
            <button
              onClick={() => {
                setCurrentStep("oauth_connect");
                setIsConnected(false); // Reset connection state when going back
                setConnectionError(null); // Clear any connection errors
                setIsInitialLoading(true); // Reset initial loading state
              }}
              className="px-4 py-2 rounded-md text-sm font-medium bg-slate-100 text-slate-700 hover:bg-slate-200"
            >
              Back
            </button>
            <button
              onClick={handleDatabaseComplete}
              disabled={!selectedProject || !selectedProject.can_continue || checkingReadiness === selectedProject?.id || isLoading}
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm font-medium disabled:bg-slate-200 disabled:text-slate-500 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {checkingReadiness === selectedProject?.id ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Checking readiness...
                </>
              ) : isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Setting up...
                </>
              ) : (
                'Complete Setup'
              )}
            </button>
          </div>
        );
      case "create_project":
        return (
          <div className="w-full flex justify-between items-center">
                    <button
          onClick={() => {
            setCurrentStep("select_project");
            setIsVisible(false); // Hide error banner when going back
            setBannerErrorMessage(""); // Clear banner error message
            setFormErrors({}); // Reset form errors when going back
            setProjectSetupStatus(""); // Reset project setup status
            setProjectSetupProgress(0); // Reset progress
          }}
          className="px-4 py-2 rounded-md text-sm font-medium bg-slate-100 text-slate-700 hover:bg-slate-200"
        >
          Back
        </button>
            <button
              onClick={handleCreateProject}
              disabled={!newProject.name.trim() || !newProject.db_password || isCreatingProject}
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm font-medium disabled:bg-slate-200 disabled:text-slate-500 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {isCreatingProject ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus size={16} />
                  Create a new project
                </>
              )}
            </button>
          </div>
        );
      default: return null;
    }
  };

  return (
    <div className="bg-white border border-slate-200 rounded-lg p-6 mt-4 max-w-2xl">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-slate-900">
            {getTitle()}
          </h3>
          {getBreadcrumb()}
        </div>
        <button 
          onClick={() => {
            onClose();
            // Reset all states when closing
            setIsConnected(false);
            setConnectionError(null);
            setCurrentStep("oauth_connect");
            setIsInitialLoading(true);
          }} 
          className="text-slate-400 hover:text-slate-600"
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      <div className="flex-1 p-6 overflow-y-auto min-h-[300px]">
        {renderContent()}
      </div>

      {currentStep !== "completing" && currentStep !== "creating_project" && currentStep !== "project_setup" && (
        <div className="px-6 py-4 bg-white border-t border-slate-200 flex-shrink-0 flex justify-end items-center space-x-3 rounded-b-xl">
          {renderFooter()}
        </div>
      )}
    </div>
  );
};

export default SupabaseOAuthChatSetup;

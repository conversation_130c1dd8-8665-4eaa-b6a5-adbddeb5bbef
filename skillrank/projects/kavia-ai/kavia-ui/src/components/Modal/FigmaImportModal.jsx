"use client";
import React, { useState, useEffect, useContext } from "react";
import { X, Plus, ExternalLink, Trash2 } from "lucide-react";
import { initiateFigmaOAuth } from "@/utils/FigmaAPI";
import { getFigmaDesignList } from "@/api/figma";
import { getHeaders } from "@/utils/api";
import Cookies from 'js-cookie';
import { AlertContext } from "@/components/NotificationAlertService/AlertList";

const FigmaImportModal = ({
  isOpen,
  onClose,
  onSubmit,
  figmaLink,
  setFigmaLink,
  designName,
  setDesignName,
  isExtractingFrames,
  showExistingDesign,
  handleBackToDesign,
  // Additional props for complete flow
  showProcessing,
  setShowProcessing,
  framesList,
  setFramesList,
  selectedFrames,
  setSelectedFrames,
  extractedFrames,
  setExtractedFrames,
  onExtractFrames,
  isProcessing,
  projectId // Add projectId prop
}) => {
  const { showAlert } = useContext(AlertContext);
  
  const [accessLevel, setAccessLevel] = useState("public");
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredFrames, setFilteredFrames] = useState([]);
  const [urlError, setUrlError] = useState('');
  
  // New state for designs list view
  const [showDesignsList, setShowDesignsList] = useState(true);
  const [showFrameSelector, setShowFrameSelector] = useState(false);
  const [designsList, setDesignsList] = useState([]);
  const [isLoadingDesigns, setIsLoadingDesigns] = useState(false);
  const [selectedDesign, setSelectedDesign] = useState(null);
  const [isFetchingScreens, setIsFetchingScreens] = useState(false);
  
  // Local state for framesList as fallback
  const [localFramesList, setLocalFramesList] = useState([]);
  
  // State to store the current figma_id for API calls
  const [currentFigmaId, setCurrentFigmaId] = useState(null);
  
  // State to track the source of the frame selector view
  const [frameSelectorSource, setFrameSelectorSource] = useState('designs'); // 'designs' or 'import'
  
  // Local state for showProcessing to ensure modal has full control
  const [localShowProcessing, setLocalShowProcessing] = useState(false);
  
  // Use local state if parent doesn't provide it, otherwise use parent's state
  const effectiveShowProcessing = showProcessing !== undefined ? showProcessing : localShowProcessing;
  const setEffectiveShowProcessing = (value) => {
    if (setShowProcessing) {
      setShowProcessing(value);
    } else {
      setLocalShowProcessing(value);
    }
  };
  
  // Add protection against duplicate API calls
  const [isImporting, setIsImporting] = useState(false);

  // Check if user is already connected to Figma on component mount
  useEffect(() => {
    const figmaUserData = localStorage.getItem('figmaUserData');
    if (figmaUserData) {
      setIsConnected(true);
      setAccessLevel("private");
    }
  }, []);

  // Reset importing state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setIsImporting(false);
      setEffectiveShowProcessing(false);
    }
  }, [isOpen]);



  // Fetch existing designs when modal opens
  useEffect(() => {
    if (isOpen && projectId && showDesignsList) {
      fetchExistingDesigns();
    } else if (isOpen && !projectId) {
      // If no projectId, default to import form
      setShowDesignsList(false);
    }
  }, [isOpen, projectId, showDesignsList]);


  // Filter frames based on search term
  useEffect(() => {
    // Use external framesList if available, otherwise use local state
    const currentFramesList = framesList || localFramesList;
    
    if (!currentFramesList) {
      setFilteredFrames([]);
      return;
    }

    if (!searchTerm.trim()) {
      setFilteredFrames(currentFramesList);
    } else {
      const filtered = currentFramesList.filter(frame =>
        frame?.screen_name?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredFrames(filtered);
    }
  }, [framesList, localFramesList, searchTerm]);

  // Function to fetch existing designs
  const fetchExistingDesigns = async () => {
    if (!projectId) return;
    
    setIsLoadingDesigns(true);
    try {
      const response = await getFigmaDesignList(projectId);
      
      if (response?.data) {
        setDesignsList(response.data);
        // Keep showing designs list even when no designs are found
      } else {
        // If no data in response, keep showing designs list
        setDesignsList([]);
      }
    } catch (error) {
      console.error("Failed to fetch designs:", error);
      // On error, keep showing designs list
      setDesignsList([]);
    } finally {
      setIsLoadingDesigns(false);
    }
  };

  // Function to handle design selection
  const handleDesignSelect = (design) => {
    setSelectedDesign(design);
    // You can add logic here to handle design selection
    // For example, show screens, extract frames, etc.
  };

  // Function to fetch design details with screens
  const fetchDesignDetails = async (projectId, figmaId) => {
    try {
      const response = await getFigmaDesignList(projectId, figmaId);
      
      if (response?.data && response.data.screen_list) {
        return response.data.screen_list;
      } else {
        console.error("Failed to fetch design details - no screen_list:", response);
        return null;
      }
    } catch (error) {
      console.error("Error fetching design details:", error);
      return null;
    }
  };

  // Function to handle extracting frames with proper figma_id
  const handleExtractFrames = () => {
    // Try to get figma_id from multiple sources
    let figmaId = currentFigmaId;
    
    // If currentFigmaId is not set, try to get it from the selected design
    if (!figmaId && selectedDesign && selectedDesign.figma_id) {
      figmaId = selectedDesign.figma_id;
    }
    
    // If still no figma_id, try to get it from the framesList
    if (!figmaId && (framesList || localFramesList)) {
      const frames = framesList || localFramesList;
      const frameWithFigmaId = frames.find(frame => frame.figma_id);
      if (frameWithFigmaId) {
        figmaId = frameWithFigmaId.figma_id;
      }
    }
    
    if (!figmaId) {
      showAlert("Error: Figma ID not available. Please try fetching screens again.", "error");
      return;
    }
    
    if (!selectedFrames || selectedFrames.size === 0) {
      showAlert("Error: No screens selected for extraction.", "error");
      return;
    }
    
    // Call the parent's onExtractFrames function with the proper data
    if (onExtractFrames) {
      const extractData = {
        figma_id: figmaId,
        selected_screen_ids: Array.from(selectedFrames)
      };
      onExtractFrames(extractData);
    }
  };

  // Function to handle fetching screens for selected design
  const handleFetchScreens = async () => {
    if (!selectedDesign) return;
    
    setIsFetchingScreens(true);
    try {
      // Set the form data from the selected design
      setFigmaLink(selectedDesign.url || '');
      setDesignName(selectedDesign.design_name || '');
      
      // Store the figma_id for later use in API calls - ensure it's properly set
      const figmaId = selectedDesign.figma_id;
      if (!figmaId) {
        throw new Error('Figma ID not available for selected design');
      }
      setCurrentFigmaId(figmaId);
      
      // Check if the design already has screen_list data
      if (selectedDesign.screen_list && selectedDesign.screen_list.length > 0) {
        // Transform the screen_list data to match the expected framesList format
        const transformedFrames = selectedDesign.screen_list.map(screen => ({
          screen_id: screen.screen_id,
          screen_name: screen.screen_name,
          Canvas: screen.Canvas,
          figma_id: figmaId, // Ensure each screen has the figma_id
          // Add any other properties that might be needed
          ...screen
        }));
        
        // Update the framesList if setFramesList is provided
        if (setFramesList) {
          setFramesList(transformedFrames);
        } else {
          // Use local state if external setter is not provided
          setLocalFramesList(transformedFrames);
        }
        
        // Go directly to frame selector view
        setShowFrameSelector(true);
        setShowDesignsList(false);
        setFrameSelectorSource('designs');
        
        // Show success notification
        showAlert(`Successfully loaded ${transformedFrames.length} screens from design`, "success");
        
      } else {
        // Try to fetch design details from API
        if (projectId && figmaId) {
          // Pass the full figma_id to the API (don't extract file key)
          const screens = await fetchDesignDetails(projectId, figmaId);
          
          if (screens && screens.length > 0) {
            // Transform the fetched screen data
            const transformedFrames = screens.map(screen => ({
              screen_id: screen.screen_id,
              screen_name: screen.screen_name,
              Canvas: screen.Canvas,
              figma_id: figmaId, // Ensure each screen has the figma_id
              // Add any other properties that might be needed
              ...screen
            }));
            
            // Update the framesList if setFramesList is provided
            if (setFramesList) {
              setFramesList(transformedFrames);
            } else {
              // Use local state if external setter is not provided
              setLocalFramesList(transformedFrames);
            }
            
            // Go directly to frame selector view
            setShowFrameSelector(true);
            setShowDesignsList(false);
            setFrameSelectorSource('designs');
            
            // Show success notification
            showAlert(`Successfully fetched ${transformedFrames.length} screens from API`, "success");
            
          } else {
            // Still go to frame selector but with empty screens
            setShowFrameSelector(true);
            setShowDesignsList(false);
            setFrameSelectorSource('designs');
          }
        } else {
          // Still go to frame selector but with empty screens
          setShowFrameSelector(true);
          setShowDesignsList(false);
          setFrameSelectorSource('designs');
        }
      }
      
    } catch (error) {
      console.error("Error fetching screens:", error);
      // Show error message to user
      showAlert("Failed to fetch screens. Please try again.", "error");
      // Still go to frame selector but with empty screens
      setShowFrameSelector(true);
      setShowDesignsList(false);
      setFrameSelectorSource('designs');
    } finally {
      setIsFetchingScreens(false);
    }
  };

  // Function to switch to import form
  const handleAddFigma = () => {
    setShowDesignsList(false);
    setShowFrameSelector(false);
    setSelectedDesign(null);
    // Reset loading states
    setIsFetchingScreens(false);
    // Reset local framesList state
    setLocalFramesList([]);
    // Reset figma_id state
    setCurrentFigmaId(null);
    // Reset frame selector source
    setFrameSelectorSource('designs');
  };

  // Function to go back to designs list
  const handleBackToDesigns = () => {
    setShowDesignsList(true);
    setSelectedDesign(null);
    setShowFrameSelector(false);
    // Reset form states
    setFigmaLink("");
    setDesignName("");
    setUrlError("");
    setAccessLevel("public");
    // Reset loading states
    setIsFetchingScreens(false);
    // Reset local framesList state
    setLocalFramesList([]);
    // Reset figma_id state
    setCurrentFigmaId(null);
    // Reset frame selector source
    setFrameSelectorSource('designs');
    
    // Refresh the designs list to show any newly added designs
    if (projectId) {
      fetchExistingDesigns();
    }
  };

  // Function to go back to import form
  const handleBackToImportForm = () => {
    setShowDesignsList(false);
    setShowFrameSelector(false);
    setEffectiveShowProcessing(false);
    setSelectedDesign(null);
    // Reset loading states
    setIsFetchingScreens(false);
    // Reset local framesList state
    setLocalFramesList([]);
    // Reset figma_id state
    setCurrentFigmaId(null);
    // Reset frame selector source
    setFrameSelectorSource('designs');
  };

  const handleOAuthConnect = async () => {
    setIsConnecting(true);
    try {
      const userId = Cookies.get('userId');
      const tenantId = Cookies.get('tenant_id');

      if (!userId || !tenantId) {
        throw new Error('User not authenticated');
      }

      // Get OAuth URL from backend
      const oauthResponse = await initiateFigmaOAuth(userId, tenantId);

      if (oauthResponse.url) {
        // Open OAuth popup
        const popup = window.open(
          oauthResponse.url,
          'figma-oauth',
          'width=600,height=700,scrollbars=yes,resizable=yes'
        );

        if (!popup) {
          // Popup was blocked
          setIsConnecting(false);
          showAlert('Popup was blocked. Please allow popups for this site and try again.', 'error');
          return;
        }

        // Listen for OAuth completion
        const handleMessage = (event) => {
          if (event.data === 'figma_connected') {
            setIsConnected(true);
            setIsConnecting(false);
            setAccessLevel("private");
            if (popup && !popup.closed) {
              popup.close();
            }
            window.removeEventListener('message', handleMessage);
            if (checkClosed) {
              clearInterval(checkClosed);
            }
          }
        };

        window.addEventListener('message', handleMessage);

        // Handle popup closed manually
        const checkClosed = setInterval(() => {
          if (popup && popup.closed) {
            clearInterval(checkClosed);
            setIsConnecting(false);
            window.removeEventListener('message', handleMessage);
          }
        }, 1000);
      }
    } catch (error) {
      console.error("OAuth connection failed:", error);
      setIsConnecting(false);
    }
  };

  const handleDisconnect = () => {
    setIsConnected(false);
    setAccessLevel("public");
  };

  // Frame selection handlers
  const handleFrameToggle = (frameId) => {
    if (extractedFrames && extractedFrames.has(frameId)) {
      return; // Do nothing if frame is already extracted
    }

    const newSelected = new Set(selectedFrames);
    if (newSelected.has(frameId)) {
      newSelected.delete(frameId);
    } else {
      newSelected.add(frameId);
    }
    setSelectedFrames(newSelected);
  };

  const handleSelectAll = () => {
    const availableFrames = filteredFrames.filter(frame =>
      !extractedFrames || !extractedFrames.has(frame.screen_id)
    );

    const selectedFromAvailable = availableFrames.filter(frame =>
      selectedFrames && selectedFrames.has(frame.screen_id)
    );

    if (selectedFromAvailable.length === availableFrames.length && availableFrames.length > 0) {
      // Deselect all
      const newSelected = new Set(selectedFrames);
      availableFrames.forEach(frame => {
        newSelected.delete(frame.screen_id);
      });
      setSelectedFrames(newSelected);
    } else {
      // Select all available
      const newSelected = new Set(selectedFrames);
      availableFrames.forEach(frame => {
        newSelected.add(frame.screen_id);
      });
      setSelectedFrames(newSelected);
    }
  };

  const getSelectAllButtonText = () => {
  const availableFrames = filteredFrames.filter(frame =>
    !extractedFrames || !extractedFrames.has(frame.screen_id)
  );

  const selectedFromAvailable = availableFrames.filter(frame =>
    selectedFrames && selectedFrames.has(frame.screen_id)
  );

  return (selectedFromAvailable.length === availableFrames.length && availableFrames.length > 0) 
    ? "Deselect All" 
    : "Select All";
};

   const validateFigmaUrl = (url) => {
    if (!url.trim()) {
      setUrlError('');
      return true; // Empty URL is valid (no error shown)
    }

    // Regular expression to match valid Figma URLs
    const figmaUrlPattern = /^https:\/\/(www\.)?figma\.com\/(file|proto|design)\/[a-zA-Z0-9]+/;
    
    if (!figmaUrlPattern.test(url)) {
      setUrlError('Please enter a valid Figma URL (e.g., https://figma.com/file/...)');
      return false;
    }
    
    setUrlError('');
    return true;
  };

  const handleFigmaLinkChange = (e) => {
    const value = e.target.value;
    setFigmaLink(value);
    
    // Validate URL in real-time
    validateFigmaUrl(value);
    
    // Auto-generate design name from URL - create a clean, readable name
    if (!designName && value) {
      try {
        // Extract the design name from the Figma URL
        const urlParts = value.split('/');
        let fileName = '';
        
        // Look for the design name in the URL path
        if (value.includes('/design/')) {
          // For design URLs, extract the name after the last slash
          const lastPart = urlParts[urlParts.length - 1];
          if (lastPart && lastPart !== '') {
            // Remove query parameters and clean up the name
            fileName = lastPart.split('?')[0];
            // Replace hyphens and underscores with spaces, capitalize words
            fileName = fileName
              .replace(/[-_]/g, ' ')
              .replace(/\b\w/g, l => l.toUpperCase())
              .trim();
          }
        } else if (value.includes('/file/')) {
          // For file URLs, extract the name after the last slash
          const lastPart = urlParts[urlParts.length - 1];
          if (lastPart && lastPart !== '') {
            fileName = lastPart.split('?')[0];
            fileName = fileName
              .replace(/[-_]/g, ' ')
              .replace(/\b\w/g, l => l.toUpperCase())
              .trim();
          }
        }
        
        // Set a clean design name or fallback
        if (fileName && fileName.length > 0) {
          setDesignName(fileName);
        } else {
          setDesignName('Figma Design');
        }
      } catch (error) {
        setDesignName('Figma Design');
      }
    }
  };

  // Function to clean and validate design name
  const getCleanDesignName = () => {
    if (!designName || designName.trim() === '') {
      return 'Figma Design';
    }
    
    // Clean the design name - remove any malformed content
    let cleanName = designName.trim();
    
    // If the name contains URL-like content, extract just the meaningful part
    if (cleanName.includes('http') || cleanName.includes('figma.com')) {
      // Extract the last meaningful part of the URL
      const urlParts = cleanName.split('/');
      const lastPart = urlParts[urlParts.length - 1];
      if (lastPart && lastPart !== '') {
        cleanName = lastPart.split('?')[0]; // Remove query parameters
        cleanName = cleanName.replace(/[-_]/g, ' '); // Replace hyphens/underscores with spaces
        cleanName = cleanName.replace(/\b\w/g, l => l.toUpperCase()); // Capitalize words
      } else {
        cleanName = 'Figma Design';
      }
    }
    
    // Ensure the name is not too long and is clean
    if (cleanName.length > 50) {
      cleanName = cleanName.substring(0, 50) + '...';
    }
    
    // Remove any remaining special characters that might cause issues
    cleanName = cleanName.replace(/[^a-zA-Z0-9\s]/g, ' ').trim();
    
    return cleanName || 'Figma Design';
  };


  const handleClearSearch = () => {
    setSearchTerm("");
  };

    const handleSubmit = async () => {
    // Show processing screen
    setEffectiveShowProcessing(true);

    try {
      // Call the parent's onSubmit function with access level and URL
      const response = await onSubmit(accessLevel, figmaLink);
      
      if (response && response.id) {
        setCurrentFigmaId(response.id);
        
        // Also ensure any existing frames have the correct figma_id
        if (framesList && setFramesList) {
          const updatedFrames = framesList.map(frame => ({
            ...frame,
            figma_id: response.id
          }));
          setFramesList(updatedFrames);
        }
        
        if (localFramesList.length > 0) {
          const updatedLocalFrames = localFramesList.map(frame => ({
            ...frame,
            figma_id: response.id
          }));
          setLocalFramesList(updatedLocalFrames);
        }
      }

      // Check if the parent response has screen data
      if (response && response.screen_list && response.screen_list.length > 0) {
        
        // Transform the screen_list data
        const transformedFrames = response.screen_list.map(screen => ({
          screen_id: screen.screen_id,
          screen_name: screen.screen_name,
          Canvas: screen.Canvas,
          canvas_id: screen.canvas_id,
          processed: screen.processed,
          figma_id: response.id || currentFigmaId, // Ensure each screen has the figma_id
          ...screen
        }));
        
        // Update the framesList
        if (setFramesList) {
          setFramesList(transformedFrames);
        } else {
          setLocalFramesList(transformedFrames);
        }
        
        // Open screen selector
        setShowFrameSelector(true);
        setEffectiveShowProcessing(false);
        setFrameSelectorSource('import');
        
        // Show success notification
        showAlert(`Successfully imported design with ${transformedFrames.length} screens`, "success");
        
        return;
      }
      
      // If parent didn't return screens, check if framesList already has screens
      const existingFrames = framesList || localFramesList;
      if (existingFrames && existingFrames.length > 0) {
        // Ensure existing frames have the proper figma_id
        const framesWithFigmaId = existingFrames.map(frame => ({
          ...frame,
          figma_id: response.id || currentFigmaId || frame.figma_id
        }));
        
        // Update the framesList with the enhanced data
        if (setFramesList) {
          setFramesList(framesWithFigmaId);
        } else {
          setLocalFramesList(framesWithFigmaId);
        }
        
        // Open screen selector with existing frames
        setShowFrameSelector(true);
        setEffectiveShowProcessing(false);
        setFrameSelectorSource('import');
        
        // Show success notification
        showAlert(`Successfully loaded ${framesWithFigmaId.length} existing screens`, "success");
        
        return;
      }
      
      // If no screens available but import was successful, show success and close modal
      if (response && (response.id || response.status === 201 || response.success)) {
        showAlert("Design imported successfully! No screens available for selection.", "success");
        setEffectiveShowProcessing(false);
        // Optionally close the modal or go back to import form
        onClose();
        return;
      }
      
      setEffectiveShowProcessing(false);
      
    } catch (error) {
      console.error("Import failed:", error);
      
      // Show error and go back to import form
      showAlert("Import failed. Please try again.", "error");
      setEffectiveShowProcessing(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-[60] flex items-center justify-center p-4">
      <div className={`w-full max-h-[90vh] bg-white rounded-lg shadow-xl overflow-hidden flex flex-col ${
        showFrameSelector ? 'max-w-4xl' : showDesignsList ? 'max-w-3xl' : 'max-w-lg'
      }`}>
        {/* Gradient Header Section */}
        <div className="relative bg-gradient-to-r from-cyan-400 via-blue-400 to-pink-400 pt-6 pb-8 flex-shrink-0">
          {/* Header with Figma Icon (center) and Close Button (right) */}
          <div className="relative flex justify-center mb-4 px-6">
            {/* Figma Icon - Centered */}
            <div className="w-12 h-12 rounded-xl flex items-center justify-center shadow-lg border-3 border-white bg-white">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 32 32" fill="none">
                <rect width="32" height="32" rx="8" fill="url(#figmaGradient)" />
                <defs>
                  <linearGradient id="figmaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#FF7262" />
                    <stop offset="25%" stopColor="#F24E1E" />
                    <stop offset="50%" stopColor="#A259FF" />
                    <stop offset="75%" stopColor="#1ABCFE" />
                    <stop offset="100%" stopColor="#0ACF83" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
            
            {/* Close Button - Absolute positioned to right */}
            <button
              onClick={onClose}
              className="absolute right-6 top-0 text-white hover:text-gray-200 transition-colors z-10"
              disabled={isExtractingFrames}
            >
              <X size={20} />
            </button>
          </div>

          {/* Title and subtitle */}
          <div className="text-center text-white">
            <h2 className="text-lg font-semibold mb-1">
              {showDesignsList ? 'Figma Designs' : 'Import Figma Design'}
            </h2>
            <p className="text-sm opacity-90">
              {showDesignsList ? 'Manage your project designs' : showFrameSelector ? 'Select screens to extract' : 'Paste any Figma file URL'}
            </p>
          </div>
        </div>

        {/* Content Section - Conditional Rendering with flexible height */}
        <div className="flex-1 overflow-y-auto min-h-0">
          {showDesignsList ? (
            // Designs List State - Initial View
            <div className="px-6 pb-6">
              {!projectId ? (
                <div className="text-center py-8 text-gray-500">
                  <svg className="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <p className="text-sm font-medium">Project ID not available</p>
                  <p className="text-xs text-gray-400 mt-1">Cannot load designs without project context</p>
                  <button
                    onClick={handleAddFigma}
                    className="mt-4 flex items-center gap-2 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg font-medium transition-colors mx-auto"
                  >
                    <Plus size={16} />
                    Add Figma
                  </button>
                </div>
              ) : (
                <>
                  <div className="flex items-center justify-between mb-4 mt-3">
                    <h2 className="text-xl font-semibold text-gray-900">List of designs for this project</h2>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={fetchExistingDesigns}
                        disabled={isLoadingDesigns}
                        className="flex items-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-lg font-medium transition-colors disabled:opacity-50"
                      >
                        <svg className={`w-4 h-4 ${isLoadingDesigns ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        {isLoadingDesigns ? 'Refreshing...' : 'Refresh'}
                      </button>
                      <button
                        onClick={handleAddFigma}
                        className="flex items-center gap-2 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                      >
                        <Plus size={16} />
                        Add Figma
                      </button>
                    </div>
                  </div>

                  {/* Designs List */}
                  <div className="space-y-3">
                    {isLoadingDesigns ? (
                      <div className="flex flex-col items-center justify-center py-8">
                        <div className="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mb-3"></div>
                        <span className="text-gray-600 font-medium">Loading designs...</span>
                        <span className="text-sm text-gray-400 mt-1">Please wait while we fetch your project designs</span>
                      </div>
                    ) : designsList.length > 0 ? (
                      designsList.map((design, index) => (
                        <div
                          key={design.figma_id || index}
                          className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                            selectedDesign?.figma_id === design.figma_id
                              ? isFetchingScreens 
                                ? 'border-orange-500 bg-orange-50 animate-pulse'
                                : 'border-orange-500 bg-orange-50'
                              : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                          }`}
                          onClick={() => !isFetchingScreens && handleDesignSelect(design)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                                selectedDesign?.figma_id === design.figma_id
                                  ? 'bg-orange-500 border-orange-500'
                                  : 'border-gray-300'
                              }`}>
                                {selectedDesign?.figma_id === design.figma_id && (
                                  <svg
                                    className="w-3 h-3 text-white"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={3}
                                      d="M5 13l4 4L19 7"
                                    />
                                  </svg>
                                )}
                              </div>
                              <div className="flex flex-col">
                                <span className="text-sm font-medium text-gray-900">
                                  {design.design_name || `Design ${index + 1}`}
                                </span>
                                <div className="flex items-center gap-2 mt-1">
                                  {design.figma_id && (
                                    <span className="text-xs text-gray-500">ID: {design.figma_id}</span>
                                  )}
                                  {design.created_at && (
                                    <span className="text-xs text-gray-400">
                                      {new Date(design.created_at).toLocaleDateString()}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {selectedDesign?.figma_id === design.figma_id && isFetchingScreens && (
                                <div className="w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full animate-spin"></div>
                              )}
                              {design.url && (
                                <a
                                  href={design.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <ExternalLink size={16} />
                                </a>
                              )}
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <svg className="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <p className="text-sm font-medium">No designs available</p>
                      </div>
                    )}
                  </div>

                  {/* Action Button for Selected Design */}
                  <div className="mt-6">
                    <button
                      onClick={handleFetchScreens}
                      disabled={!selectedDesign || isFetchingScreens}
                      className={`w-full py-3 px-6 rounded-lg font-medium text-base transition-all flex items-center justify-center gap-3 ${
                        !selectedDesign || isFetchingScreens
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : 'bg-orange-500 hover:bg-orange-600 shadow-md hover:shadow-lg text-white'
                      }`}
                    >
                      {isFetchingScreens ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          <span>Fetching screens...</span>
                        </>
                      ) : (
                        <>
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                          </svg>
                          <span>Fetch screens for this design</span>
                        </>
                      )}
                    </button>
                  </div>
                </>
              )}
            </div>
          ) : showProcessing ? (
            // Processing State
            <div className="px-6 pb-6 text-center">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Processing</h2>
              <p className="text-gray-600 mb-8">We are fetching the screens.</p>

              {/* Processing Animation */}
              <div className="flex items-center justify-center gap-4 mb-8">
                <div className="w-12 h-12 bg-black rounded-lg flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 32 32" fill="none">
                    <rect width="32" height="32" rx="8" fill="url(#figmaGradient)" />
                    <defs>
                      <linearGradient id="figmaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#FF7262" />
                        <stop offset="25%" stopColor="#F24E1E" />
                        <stop offset="50%" stopColor="#A259FF" />
                        <stop offset="75%" stopColor="#1ABCFE" />
                        <stop offset="100%" stopColor="#0ACF83" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>

                {/* Animated dots */}
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                </div>

                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
              </div>
            </div>
          ) : showFrameSelector ? (
            // Frame Selection State
            <div className="px-6 pb-6 flex flex-col h-full">
              {/* Validation check for figma_id */}
              {(() => {
                // Try to get figma_id from multiple sources
                let figmaId = currentFigmaId;
                
                if (!figmaId && selectedDesign && selectedDesign.figma_id) {
                  figmaId = selectedDesign.figma_id;
                }
                
                if (!figmaId && (framesList || localFramesList)) {
                  const frames = framesList || localFramesList;
                  const frameWithFigmaId = frames.find(frame => frame.figma_id);
                  if (frameWithFigmaId) {
                    figmaId = frameWithFigmaId.figma_id;
                  }
                }
                
                if (!figmaId) {
                  return (
                    <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center gap-2 text-red-700">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="font-medium">Warning: Figma ID not available</span>
                      </div>
                      <p className="text-sm text-red-600 mt-1">
                        The Figma ID is required for screen extraction. Please go back and try fetching screens again.
                      </p>
                    </div>
                  );
                }
                return null;
              })()}
              
              <div className="flex items-center justify-between mb-4 mt-3">
                <h2 className="text-xl font-semibold text-gray-900">Select Screens to Extract</h2>
                <div className="flex items-center gap-2 flex-wrap">
                  <button
                    onClick={handleSelectAll}
                    className="text-sm font-medium text-orange-600 hover:text-orange-700 bg-orange-50 hover:bg-orange-100 px-3 py-1 rounded-md transition-colors"
                  >
                    {getSelectAllButtonText()}
                  </button>
                  <button
                    onClick={frameSelectorSource === 'import' ? handleBackToImportForm : handleBackToDesigns}
                    className="flex items-center gap-1 border border-orange-500 text-orange-500 hover:bg-orange-50 rounded px-3 py-1.5 text-sm font-medium"
                  >
                    ← {frameSelectorSource === 'import' ? 'Back to Import' : 'Back'}
                  </button>
                  <button
                    onClick={handleAddFigma}
                    className="flex items-center gap-1 border border-orange-500 text-orange-500 hover:bg-orange-50 rounded px-3 py-1.5 text-sm font-medium"
                  >
                    + Add Figma
                  </button>
                </div>
              </div>

              {/* Search Bar */}
              <div className="mb-4">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    placeholder="Search screens by name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm bg-white shadow-sm"
                  />
                  {searchTerm && (
                    <button
                      onClick={handleClearSearch}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    </button>
                  )}
                </div>
              </div>

              {/* Frames List - Flexible height */}
              <div className="flex-1 min-h-0 border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm mb-4">
                {filteredFrames && filteredFrames.length > 0 ? (
                  <div className="p-4 space-y-2 h-full overflow-y-auto">
                    {filteredFrames.map((frame, index) => {
                      const isSelected = selectedFrames && selectedFrames.has(frame.screen_id);
                      const isExtracted = extractedFrames && extractedFrames.has(frame.screen_id);

                      return (
                        <div
                          key={frame.screen_id}
                          className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ${
                            isExtracted
                              ? 'bg-green-50 border-green-200 cursor-not-allowed'
                              : isSelected
                                ? 'bg-orange-50 border-orange-200 cursor-pointer shadow-sm'
                                : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300 cursor-pointer'
                          }`}
                          onClick={() => !isExtracted && handleFrameToggle(frame.screen_id)}
                        >
                          <div className="flex items-center gap-3">
                            <div
                              className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200 ${
                                isExtracted
                                  ? 'bg-green-100 border-green-400'
                                  : isSelected
                                    ? 'bg-orange-500 border-orange-500 shadow-sm'
                                    : 'border-gray-300 hover:border-orange-400'
                              }`}
                            >
                              {(isExtracted || isSelected) && (
                                <svg
                                  className={`w-3 h-3 ${isExtracted ? 'text-green-600' : 'text-white'}`}
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={3}
                                    d="M5 13l4 4L19 7"
                                  />
                                </svg>
                              )}
                            </div>
                            <div className="flex flex-col">
                              <span className="text-sm font-medium text-gray-900">
                                {frame?.screen_name || `Screen ${index + 1}`}
                              </span>
                              {isExtracted && (
                                <span className="text-xs text-green-600">Already extracted</span>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center">
                            <div className={`w-2 h-2 rounded-full ${
                              isExtracted ? 'bg-green-500' : isSelected ? 'bg-orange-500' : 'bg-gray-300'
                            }`}></div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : isFetchingScreens ? (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500 p-6">
                    <div className="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mb-3"></div>
                    <p className="text-sm font-medium">Fetching screens...</p>
                    <p className="text-xs text-gray-400 mt-1">Please wait while we load the screens from your Figma design</p>
                  </div>
                ) : (framesList || localFramesList) && (framesList || localFramesList).length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500 p-6">
                    <svg className="w-12 h-12 mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p className="text-sm font-medium">No screens found</p>
                    <p className="text-xs text-gray-400 mt-1">The Figma design doesn't contain any screens or frames</p>
                    <p className="text-xs text-gray-400 mt-1">URL: {figmaLink}</p>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500 p-6">
                    <svg className="w-12 h-12 mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p className="text-sm font-medium">No screens available</p>
                    <p className="text-xs text-gray-400 mt-1">There are no screens available for the uploaded Figma design.</p>
                    <p className="text-xs text-gray-400 mt-1">Debug: framesList length = {framesList ? framesList.length : 'undefined'}, localFramesList length = {localFramesList ? localFramesList.length : 'undefined'}</p>
                  </div>
                )}
              </div>

              {/* Extract Button */}
              <button
                onClick={handleExtractFrames}
                disabled={!selectedFrames || selectedFrames.size === 0 || isExtractingFrames}
                className={`w-full py-3 px-6 rounded-lg font-medium text-base transition-all flex items-center justify-center gap-3 ${
                  !selectedFrames || selectedFrames.size === 0 || isExtractingFrames
                    ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    : 'bg-orange-500 hover:bg-orange-600 text-white shadow-md hover:shadow-lg'
                }`}
            >
              {isExtractingFrames ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Extracting screens...</span>
                </>
              ) : (
                <>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                  </svg>
                  <span>Extract Selected Screens ({selectedFrames ? selectedFrames.size : 0})</span>
                </>
              )}
            </button>
          </div>
        ) : (
          // Initial Form State
          <div className="px-6 pb-6">


            {/* Access Level Section */}
            <div className="mb-8 mt-6">
              <div className="mb-3">
                <h3 className="text-sm font-medium text-gray-900">Access Level</h3>
              </div>
              <div className="grid grid-cols-2 gap-3">
                {/* Public Only Option */}
                <button
                  onClick={() => setAccessLevel("public")}
                  className={`p-4 rounded-lg border-2 text-left transition-all ${accessLevel === "public"
                      ? "border-blue-500 bg-blue-50 shadow-sm"
                      : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                    }`}
                  >
                    <div className="font-semibold text-gray-900 mb-1">Public Only</div>
                    <div className="text-xs text-gray-500">No login required</div>
                  </button>

                  {/* Public + Private Option */}
                  <button
                    onClick={() => setAccessLevel("private")}
                    className={`p-4 rounded-lg border-2 text-left transition-all ${
                      accessLevel === "private"
                        ? "border-blue-500 bg-blue-50 shadow-sm"
                        : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                    }`}
                  >
                    <div className="font-semibold text-gray-900 mb-1">Public + Private</div>
                    <div className="text-xs text-gray-500">OAuth required</div>
                  </button>
                </div>
              </div>

              {/* OAuth Connection Button (shown when Private is selected) */}
              {accessLevel === "private" && (
                <div className="mt-6 mb-8">
                  {isConnected ? (
                    <div className="space-y-3">
                      <div className="flex items-center justify-center gap-2 bg-green-50 border border-green-200 rounded-lg p-4">
                        <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                          <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                        <span className="text-green-700 font-medium">Connected to Figma</span>
                      </div>
                      <button
                        onClick={handleDisconnect}
                        className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors text-sm"
                      >
                        Disconnect
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={handleOAuthConnect}
                      disabled={isConnecting}
                      className={`w-full py-3 px-4 rounded-lg font-medium transition-all ${
                        isConnecting
                          ? "bg-blue-300 cursor-not-allowed"
                          : "bg-blue-500 hover:bg-blue-600"
                      } text-white flex items-center justify-center gap-2`}
                    >
                      {isConnecting ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          Connecting...
                        </>
                      ) : (
                        "Connect to Figma"
                      )}
                    </button>
                  )}
                </div>
              )}

              {/* Figma File URL Section */}
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Figma File URL</h3>
                <input
                  type="url"
                  value={figmaLink}
                  onChange={handleFigmaLinkChange}
                  className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 transition-all placeholder-gray-400 ${
                    urlError 
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50' 
                      : 'border-gray-300 focus:ring-blue-500 focus:border-transparent'
                  }`}
                  placeholder="https://figma.com/file/..."
                  disabled={isExtractingFrames || (accessLevel === "private" && !isConnected)}
                />
                {urlError && (
                  <div className="flex items-center gap-2 mt-2 text-red-600">
                    <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-xs font-medium">{urlError}</p>
                  </div>
                )}
                
                {/* Helper Text */}
                <p className={`text-xs mt-2 ${
                  urlError ? 'text-red-500' : accessLevel === "private" && !isConnected
                    ? "text-orange-600"
                    : "text-gray-500"
                }`}>
                  {urlError ? 'Valid formats: https://figma.com/file/... or https://figma.com/design/...' : 
                  accessLevel === "private"
                    ? isConnected
                      ? "Paste any public or private Figma file URL"
                      : "Connect to Figma first to access private files"
                    : "Paste any public Figma file URL"
                  }
                </p>
              </div>

              {/* Bottom Section with View Existing Designs link and Action Buttons */}
              <div className="flex items-center justify-between">
                {/* View Existing Designs Link */}
                <button
                  onClick={handleBackToDesigns}
                  className="inline-flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors"
                >
                  <span>← View Existing Designs</span>
                </button>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <button
                    onClick={onClose}
                    disabled={isExtractingFrames}
                    className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSubmit}
                    disabled={isExtractingFrames || !figmaLink.trim() || urlError || (accessLevel === "private" && (!isConnected || isConnecting))}
                    className={`px-6 py-3 rounded-lg font-medium transition-all ${
                      isExtractingFrames || !figmaLink.trim() || urlError || (accessLevel === "private" && (!isConnected || isConnecting))
                        ? "bg-blue-300 cursor-not-allowed"
                        : "bg-blue-500 hover:bg-blue-600"
                    } text-white`}
                  >
                    {isExtractingFrames ? "Importing..." : "Import"}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FigmaImportModal;

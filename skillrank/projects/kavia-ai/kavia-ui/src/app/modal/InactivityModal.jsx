import React, { useEffect, useRef, useState } from 'react';
import { Clock, AlertCircle } from 'lucide-react';
import { useCodeGeneration } from '@/components/Context/CodeGenerationContext';

import { useSearchParams, useRouter, useParams } from 'next/navigation';

const InactivityTimerModal = () => {
    const { timeoutWarning, setTimeoutWarning, wsConnection, setStatus } = useCodeGeneration();
    const [localSecondsRemaining, setLocalSecondsRemaining] = useState(0);
    const [redirectCountdown, setRedirectCountdown] = useState(3);
    const intervalRef = useRef(null);
    const redirectIntervalRef = useRef(null);
    const lastUpdateRef = useRef(null);
    const searchParams = useSearchParams();
    const router = useRouter();
    const params = useParams();

    useEffect(() => {
        if (timeoutWarning.show && timeoutWarning.secondsRemaining > 0) {
            setLocalSecondsRemaining(timeoutWarning.secondsRemaining);
            lastUpdateRef.current = Date.now();
            
            // Clear any existing interval
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
            
            // Start countdown timer
            intervalRef.current = setInterval(() => {
                setLocalSecondsRemaining(prev => {
                    if (prev <= 1) {
                        clearInterval(intervalRef.current);
                        intervalRef.current = null;
                        handleLogout();
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);
        }

        // Cleanup interval when modal is hidden
        if (!timeoutWarning.show && intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }

        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        };
    }, [timeoutWarning.secondsRemaining, timeoutWarning.show]);

    // Handle auto-redirect countdown when session is terminated
    useEffect(() => {
        if (timeoutWarning.show && timeoutWarning.terminated) {
            setRedirectCountdown(3);
            
            // Clear any existing redirect interval
            if (redirectIntervalRef.current) {
                clearInterval(redirectIntervalRef.current);
            }
            
            // Start redirect countdown
            redirectIntervalRef.current = setInterval(() => {
                setRedirectCountdown(prev => {
                    if (prev <= 1) {
                        clearInterval(redirectIntervalRef.current);
                        redirectIntervalRef.current = null;
                        handleRedirectToCode();
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);
        }

        // Cleanup redirect interval when modal is hidden or not terminated
        if (!timeoutWarning.show || !timeoutWarning.terminated) {
            if (redirectIntervalRef.current) {
                clearInterval(redirectIntervalRef.current);
                redirectIntervalRef.current = null;
            }
        }

        return () => {
            if (redirectIntervalRef.current) {
                clearInterval(redirectIntervalRef.current);
            }
        };
    }, [timeoutWarning.show, timeoutWarning.terminated]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
            if (redirectIntervalRef.current) {
                clearInterval(redirectIntervalRef.current);
            }
        };
    }, []);

    const handleLogout = () => {
        setStatus("Auto Stopped");
    };

    const handleRedirectToCode = () => {
        const organizationId = params.organization_id;
        const projectId = params.projectId;
        const task_id = searchParams.get('task_id');
        
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.delete("task_id");
        
        router.push(`/${organizationId}/project/${projectId}/code`);
             
        // Close the modal after redirect
        setTimeoutWarning({ show: false, secondsRemaining: 0, terminated: false });
    };

    const handleResetActivity = () => {
        if (wsConnection?.readyState === WebSocket.OPEN) {
            wsConnection.send(JSON.stringify({
                type: 'ready_check'
            }));
        }
        setTimeoutWarning({ show: false, secondsRemaining: 0, terminated: false });
        setLocalSecondsRemaining(0);
        
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }
    };

    const handleCloseTerminated = () => {
        // Clear redirect interval to abort redirection
        if (redirectIntervalRef.current) {
            clearInterval(redirectIntervalRef.current);
            redirectIntervalRef.current = null;
        }
        
        setTimeoutWarning({ show: false, secondsRemaining: 0, terminated: false });
        setLocalSecondsRemaining(0);
        setRedirectCountdown(3);
        
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }
    };

    if (!timeoutWarning.show) return null;

    // If session has been terminated, show different UI with redirect countdown
    if (timeoutWarning.terminated) {
        return (
            <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
                <div className="bg-gray-900 border border-gray-700 rounded-xl shadow-2xl max-w-md w-full p-6 relative overflow-hidden">
                    <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-red-500 to-red-600"></div>
                    <div className="text-center pt-2">
                        <div className="mx-auto flex items-center justify-center h-14 w-14 rounded-full bg-red-500/10 border-2 border-red-500/20 mb-4">
                            <AlertCircle className="h-7 w-7 text-red-400" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">Session Closed</h3>
                        <p className="text-gray-300 text-sm mb-4">
                            Your code generation session has been automatically closed due to inactivity.
                        </p>
                        <div className="bg-gray-800 rounded-lg p-4 mb-4 border border-gray-700">
                            <div className="text-sm text-gray-400 mb-2">
                                All progress has been saved automatically.
                            </div>
                            <div className="text-sm text-gray-300">
                                You can start a new session anytime.
                            </div>
                        </div>
                        <div className="bg-gray-800/50 rounded-lg p-3 mb-6 border border-gray-600">
                            <div className="text-sm text-yellow-400 mb-1 font-medium">
                                Auto-redirecting to code page in:
                            </div>
                            <div className="text-xl font-mono font-bold text-yellow-300">
                                {redirectCountdown}s
                            </div>
                        </div>
                        <div className="flex justify-center">
                            <button
                                onClick={handleCloseTerminated}
                                className="px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg"
                            >
                                Cancel & Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Normal timeout warning UI
    return (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-gray-900 border border-gray-700 rounded-xl shadow-2xl max-w-md w-full p-6 relative overflow-hidden">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-500 to-red-500"></div>
                <div className="text-center pt-2">
                    <div className="mx-auto flex items-center justify-center h-14 w-14 rounded-full bg-primary/10 border-2 border-primary/20 mb-4">
                        <Clock className="h-7 w-7 text-primary-400" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-2">Inactivity Detected</h3>
                    <p className="text-gray-300 text-sm mb-4">
                        You've been inactive for a while. Your session will automatically end in:
                    </p>
                    <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
                        <div className="text-2xl font-mono font-bold text-primary-400 mb-1">
                            {localSecondsRemaining}s
                        </div>
                    </div>
                    <div className="flex justify-center">
                        <button
                            onClick={handleResetActivity}
                            className="px-6 py-3 bg-gradient-to-r from-primary-500 to-red-500 hover:from-primary-600 hover:to-red-600 text-white font-medium rounded-lg transition-all duration-200 shadow-lg"
                        >
                            Reset Activity
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default InactivityTimerModal;